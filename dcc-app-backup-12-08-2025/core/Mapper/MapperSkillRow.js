import { useState } from "react";
import { Card, CardContent } from "@core/components/ui/card";
import { Checkbox } from "@core/components/ui/checkbox";
import { Label } from "@core/components/ui/label";

const MapperCardTech = ({ desc, level, isChecked, handleOnChange }) => {
  return (
    <Label className="hover:bg-accent/50 flex items-start gap-3 border p-3 has-[[aria-checked=true]]:border-yellow-500 has-[[aria-checked=true]]:bg-yellow-50 dark:has-[[aria-checked=true]]:border-yellow-500 dark:has-[[aria-checked=true]]:bg-yellow-400">
      <Checkbox
        id={1}
        type="checkbox"
        checked={isChecked}
        onCheckedChange={() => handleOnChange(level)}
        className="data-[state=checked]:border-yellow-500 data-[state=checked]:bg-yellow-500 data-[state=checked]:text-white dark:data-[state=checked]:border-yellow-500 dark:data-[state=checked]:bg-yellow-500"
      />
      <div className="inline-block align-middle">{desc}</div>
    </Label>
  );
};

const MapperSkillRow = ({
  technical_sub_skills,
  handleOnChange,
  lvl1,
  lvl2,
  lvl3,
  lvl4,
}) => {
  // put them in index order here
  technical_sub_skills.sort((a, b) => a.index - b.index);

  return technical_sub_skills.map((skill, index) => (
    <>
      <Card
        className={
          index == 0
            ? "min-w-8  bg-[#1f144a] text-primary-foreground rounded-tl-xl rounded-tr-xl"
            : "min-w-8  bg-[#1f144a] text-primary-foreground"
        }
      >
        <CardContent className={"font-extrabold text-l text-center"}>
          <div className="inline-block align-middle">
            {technical_sub_skills[index].sub_skill_name}
          </div>
        </CardContent>
      </Card>

      <MapperCardTech
        desc={technical_sub_skills[0].level_1_description}
        level={1}
        isChecked={lvl1}
        handleOnChange={handleOnChange}
      />
      <MapperCardTech
        desc={technical_sub_skills[0].level_2_description}
        level={2}
        isChecked={lvl2}
        handleOnChange={handleOnChange}
      />
      <MapperCardTech
        desc={technical_sub_skills[0].level_3_description}
        level={3}
        isChecked={lvl3}
        handleOnChange={handleOnChange}
      />
      <MapperCardTech
        desc={technical_sub_skills[0].level_4_description}
        level={4}
        isChecked={lvl4}
        handleOnChange={handleOnChange}
      />

      {/* <Card className="min-w-8 bg-muted/50 hover:bg-primary hover:text-white">
        <CardContent className="text-sm tracking-tight]">
          {technical_sub_skills[index].level_1_description}
        </CardContent>
      </Card>
      <Card className="min-w-8 bg-muted/50 hover:bg-primary hover:text-white">
        <CardContent className="text-sm tracking-tight]">
          {technical_sub_skills[index].level_2_description}
        </CardContent>
      </Card>
      <Card className="min-w-8 bg-muted/50 hover:bg-primary hover:text-white">
        <CardContent className="text-sm tracking-tight]">
          {technical_sub_skills[index].level_3_description}
        </CardContent>
      </Card>
      <Card className="min-w-8 bg-muted/50 hover:bg-primary hover:text-white">
        <CardContent className="text-sm tracking-tight]">
          {technical_sub_skills[index].level_4_description}
        </CardContent>
      </Card> */}
    </>
  ));
};

export default MapperSkillRow;
