"use client";

import { useState, useEffect } from "react";
import { ChevronRight, Combine } from "lucide-react";

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@core/components/ui/collapsible";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@core/components/ui/sidebar";
import Link from "next/link";

export function NavAdmin({
  userData,
  allMappings,
  deleteMappingRecord,
  typeSelected,
  selected_item,
}) {
  const [skills, setskills] = useState(allMappings);

  const removeItem = (id) => {
    const updatedSkills = [...skills];
    const index = updatedSkills.findIndex((skill) => skill.id === id);
    if (index !== -1) {
      updatedSkills.splice(index, 1);
    }
    setskills(updatedSkills);
  };

  return (
    <SidebarGroup>
      <SidebarGroupLabel className="min-w-8 bg-secondary text-primary-foreground">
        Admin tools
      </SidebarGroupLabel>
      <SidebarMenu>
        <Collapsible
          key={1}
          asChild
          defaultOpen={selected_item}
          className="group/collapsible"
        >
          <SidebarMenuItem>
            <CollapsibleTrigger asChild>
              <SidebarMenuButton tooltip={"Admin tools"}>
                <Combine />
                {/* <Link href="/admin/skills-mapping"> */}
                <span>Skills mapping</span> {/* </Link> */}
                <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
              </SidebarMenuButton>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <SidebarMenuSub>
                <SidebarMenuSubItem key="behavioural">
                  <SidebarMenuSubButton asChild>
                    <Link href={"/admin/skills-mapping-behavioural"}>
                      {selected_item && selected_item === "behavioural" ? (
                        <span className="text-primary font-bold">
                          Behavioural
                        </span>
                      ) : (
                        <span>Behavioural</span>
                      )}
                    </Link>
                  </SidebarMenuSubButton>
                </SidebarMenuSubItem>
                <SidebarMenuSubItem key="technical">
                  <SidebarMenuSubButton asChild>
                    <Link href={"/admin/skills-mapping-technical"}>
                      {selected_item && selected_item === "technical" ? (
                        <span className="text-primary font-bold">
                          Technical
                        </span>
                      ) : (
                        <span>Technical</span>
                      )}
                    </Link>
                  </SidebarMenuSubButton>
                </SidebarMenuSubItem>
              </SidebarMenuSub>
            </CollapsibleContent>

            {typeSelected && (
              <div className="p-3">
                <div>
                  <h1 className="text-sm font-extrabold tracking-tight text-balance text-dccblue pb-2">
                    Skills mapped:{" "}
                    {allMappings && allMappings.length + " / " + " 12"}
                  </h1>
                </div>
                <div className="flex flex-wrap gap-2">
                  {skills &&
                    skills.map((skill) => (
                      <div
                        id={skill.id}
                        class=" mb-1 relative rounded-md flex bg-dccdarkgrey py-0.5 pl-2.5 pr-8 border border-transparent text-sm text-white transition-all shadow-sm font-semibold"
                      >
                        {skill.skill_name}
                        <button
                          className="flex items-center justify-center transition-all p-1 rounded-md text-white hover:bg-white/10 active:bg-white/10 absolute top-0.5 right-0.5"
                          type="button"
                          onClick={() => {
                            deleteMappingRecord(skill.id);
                            removeItem(skill.id);
                          }}
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 16 16"
                            fill="currentColor"
                            class="w-4 h-4"
                          >
                            <path d="M5.28 4.22a.75.75 0 0 0-1.06 1.06L6.94 8l-2.72 2.72a.75.75 0 1 0 1.06 1.06L8 9.06l2.72 2.72a.75.75 0 1 0 1.06-1.06L9.06 8l2.72-2.72a.75.75 0 0 0-1.06-1.06L8 6.94 5.28 4.22Z" />
                          </svg>
                        </button>
                      </div>
                    ))}
                </div>
              </div>
            )}
          </SidebarMenuItem>
        </Collapsible>
      </SidebarMenu>
    </SidebarGroup>
  );
}
