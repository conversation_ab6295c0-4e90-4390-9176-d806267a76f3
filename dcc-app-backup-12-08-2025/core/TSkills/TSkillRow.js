import { Card, CardContent } from "@core/components/ui/card";

const TSkillsLibHeader = ({ technical_sub_skills }) => {
  // put them in index order here
  technical_sub_skills.sort((a, b) => a.index - b.index);

  return technical_sub_skills.map((skill, index) => (
    <>
      <Card
        className={
          index == 0
            ? "min-w-8  bg-[#1f144a] text-primary-foreground rounded-tl-xl rounded-tr-xl"
            : "min-w-8  bg-[#1f144a] text-primary-foreground"
        }
      >
        <CardContent className={"font-extrabold text-l text-center"}>
          <div className="inline-block align-middle">
            {technical_sub_skills[index].sub_skill_name}
          </div>
        </CardContent>
      </Card>

      <Card className="min-w-8 bg-muted/50 hover:bg-primary hover:text-white">
        <CardContent className="text-sm tracking-tight]">
          {technical_sub_skills[index].level_1_description}
        </CardContent>
      </Card>
      <Card className="min-w-8 bg-muted/50 hover:bg-primary hover:text-white">
        <CardContent className="text-sm tracking-tight]">
          {technical_sub_skills[index].level_2_description}
        </CardContent>
      </Card>
      <Card className="min-w-8 bg-muted/50 hover:bg-primary hover:text-white">
        <CardContent className="text-sm tracking-tight]">
          {technical_sub_skills[index].level_3_description}
        </CardContent>
      </Card>
      <Card className="min-w-8 bg-muted/50 hover:bg-primary hover:text-white">
        <CardContent className="text-sm tracking-tight]">
          {technical_sub_skills[index].level_4_description}
        </CardContent>
      </Card>
    </>
  ));
};

export default TSkillsLibHeader;
