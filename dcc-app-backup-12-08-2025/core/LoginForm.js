import React from "react";
import { useState, useEffect } from "react";
import { supabase } from "@utils/supabaseClient";
import { Oval } from "svg-loaders-react";
import { useRouter } from "next/router";
import Image from "next/image";

// import {y
//   InputOTP,
//   InputOTPGroup,
//   InputOTPSeparator,
//   InputOTPSlot,
// } from "@core/components/ui/input-otp";

export default function LoginForm() {
  const [loading, setLoading] = useState(false);
  const [email, setEmail] = useState(null);
  const [passCode, setPassCode] = useState("");
  const [showPasscode, setshowPasscode] = useState(false);
  const [errorOnSubmit, setErrorOnSubmit] = useState(false);
  const [session, setSession] = useState(null);
  const router = useRouter();

  /* --- DEBUG --- */
  // console.log("session");
  // console.log(session);
  /* --- DEBUG --- */

  useEffect(() => {
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
    });

    supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
    });
  }, []);

  const handleLogin = async (email) => {
    try {
      setLoading(true);
      const { data, error } = await supabase.auth.signInWithOtp({
        email: email,
        options: {
          // set this to false if you do not want the user to be automatically signed up
          shouldCreateUser: false,
        },
      });
      if (error) {
        throw error;
      } else {
        // console.log(data);
        setshowPasscode(true);
        // router.push("/dashboard");
      }
    } catch (error) {
      // console.log(error.error_description || error.message);
    } finally {
      setLoading(false);
    }
  };

  const sendPasscode = async () => {
    try {
      setLoading(true);
      const {
        data: { session },
        error,
      } = await supabase.auth.verifyOtp({
        email: "<EMAIL>",
        token: passCode,
        type: "email",
      });
      if (error) {
        throw error;
      } else {
        // console.log(session);
      }
    } catch (error) {
      // console.log(error.error_description || error.message);
      setErrorOnSubmit(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex flex-col justify-center py-3 sm:px-6 lg:px-8">
      {loading ? (
        <div className="grid place-items-center">
          <div className="mt-10 mb-10 flex flex-row space-x-2">
            <Oval stroke="#0c39ac" />
          </div>
        </div>
      ) : (
        <>
          <div className="sm:mx-auto sm:w-full sm:max-w-md">
            <div className="mx-auto flex justify-center p-6">
              <Image
                src="/logo/dcc-logo.svg"
                alt="DCC"
                width={250}
                height={100}
              />
            </div>

            <h2 className="font-Ubuntu mt-6 text-center text-2xl font-extrabold text-gray-900">
              Sign into your account
            </h2>
          </div>

          <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md m-6">
            <div className="bg-white">
              {loading ? (
                <div className="grid place-items-center">
                  <div className="mt-10 mb-10 flex flex-row space-x-2">
                    <Oval stroke="#0c39ac" />
                  </div>
                </div>
              ) : (
                <div>
                  {showPasscode ? (
                    <div className="grid place-items-center">
                      <div className="flex flex-row space-x-2 mb-6">
                        <p>
                          Please check your email for a passcode
                          <br /> and enter it here to login
                        </p>
                      </div>

                      <div className="max-w-sm min-w-[20px]">
                        <div className="relative">
                          {/* <InputOTP
                        maxLength={6}
                        value={passCode || ""}
                        onChange={(e) => setPassCode(e.target.value)}
                        type="passcode"
                        required
                      >
                        <InputOTPGroup>
                          <InputOTPSlot index={0} />
                          <InputOTPSlot index={1} />
                          <InputOTPSlot index={2} />
                        </InputOTPGroup>
                        <InputOTPSeparator />
                        <InputOTPGroup>
                          <InputOTPSlot index={3} />
                          <InputOTPSlot index={4} />
                          <InputOTPSlot index={5} />
                        </InputOTPGroup>
                      </InputOTP> */}

                          <input
                            value={passCode || ""}
                            onChange={(e) => setPassCode(e.target.value)}
                            type="passcode"
                            maxLength={6}
                            required
                            className="w-full bg-transparent placeholder:text-slate-400 text-slate-700 text-lg border border-slate-200 rounded-md pl-3 py-2 transition duration-300 ease focus:outline-none focus:border-slate-400 hover:border-slate-300 shadow-sm focus:shadow"
                            placeholder="passcode"
                          />
                          <button
                            className="absolute right-2 top-2 rounded bg-primary py-1 px-2.5 border border-transparent text-center text-sm text-white transition-all shadow-sm hover:shadow focus:bg-green-500 focus:shadow-none active:bg-green-700 hover:bg-green-700 active:shadow-none disabled:pointer-events-none disabled:opacity-50 disabled:shadow-none"
                            type="button"
                            onClick={sendPasscode}
                          >
                            Sign in
                          </button>
                        </div>
                      </div>
                      {errorOnSubmit ? (
                        <div className="text-error pt-6">{errorOnSubmit}</div>
                      ) : null}
                    </div>
                  ) : (
                    <div>
                      <div>
                        <label
                          htmlFor="email"
                          className="block text-sm font-medium text-gray-700"
                        >
                          Email address
                        </label>
                        <div className="mt-1">
                          <input
                            type="email"
                            placeholder="Your email"
                            value={email || ""}
                            onChange={(e) => setEmail(e.target.value)}
                            required
                            className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                          />
                        </div>
                      </div>
                      <div className="mt-6">
                        <button
                          onClick={(e) => {
                            e.preventDefault();
                            handleLogin(email);
                          }}
                          disabled={loading}
                          className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        >
                          Sign in
                        </button>

                        <div className="mt-0 flex flex-row space-x-2">
                          {!errorOnSubmit ? (
                            <div className="text-error pt-6">
                              {errorOnSubmit}
                            </div>
                          ) : null}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  );
}
