{"version": 3, "names": ["module", "exports", "import_", "filepath"], "sources": ["../../../src/config/files/import.cjs"], "sourcesContent": ["// We keep this in a separate file so that in older node versions, where\n// import() isn't supported, we can try/catch around the require() call\n// when loading this file.\n\nmodule.exports = function import_(filepath) {\n  return import(filepath);\n};\n"], "mappings": "AAIAA,MAAM,CAACC,OAAO,GAAG,SAASC,OAAOA,CAACC,QAAQ,EAAE;EAC1C,OAAO,OAAOA,QAAQ,CAAC;AACzB,CAAC;AAAC", "ignoreList": []}