{"version": 3, "names": ["_core", "require", "_assert", "_helperAnnotateAsPure", "_helperSkipTransparentExpressionWrappers", "ENUMS", "WeakMap", "buildEnumWrapper", "template", "expression", "transpileEnum", "path", "t", "node", "parentPath", "declare", "remove", "name", "id", "fill", "data", "isPure", "enumFill", "type", "isGlobal", "isProgram", "parent", "isSeen", "seen", "init", "objectExpression", "logicalExpression", "cloneNode", "ID", "enumIIFE", "Object", "assign", "INIT", "annotateAsPure", "toReplace", "isExportDeclaration", "replaceWith", "expressionStatement", "assignmentExpression", "scope", "registerDeclaration", "variableDeclaration", "variableDeclarator", "set", "getBindingIdentifier", "Error", "getData", "setData", "buildStringAssignment", "statement", "buildNumericAssignment", "buildEnumMember", "isString", "options", "enum<PERSON><PERSON><PERSON>", "translateEnumValues", "enumMembers", "get", "assignments", "i", "length", "memberName", "memberValue", "push", "inheritsComments", "isSyntacticallyString", "ENUM", "NAME", "VALUE", "ASSIGNMENTS", "expr", "skipTransparentExprWrapperNodes", "left", "right", "operator", "ReferencedIdentifier", "state", "has", "curScope", "hasOwnBinding", "memberExpression", "skip", "enumSelfReferenceVisitor", "_ENUMS$get", "bindingIdentifier", "Map", "constV<PERSON>ue", "lastName", "map", "memberPath", "member", "isIdentifier", "value", "initializerPath", "initializer", "computeConstantValue", "undefined", "assert", "Infinity", "Number", "isNaN", "identifier", "String", "unaryExpression", "valueToNode", "isReferencedIdentifier", "traverse", "numericLiteral", "buildCodeFrameError", "lastRef", "stringLiteral", "binaryExpression", "prevMembers", "Set", "evaluate", "evaluateRef", "evalUnaryExpression", "evalBinaryExpression", "quasis", "cooked", "paths", "str", "isMemberExpression", "obj", "object", "prop", "property", "computed", "isStringLiteral", "includes", "add", "resolve", "Math", "pow"], "sources": ["../src/enum.ts"], "sourcesContent": ["import { template, types as t, type NodePath } from \"@babel/core\";\nimport assert from \"node:assert\";\nimport annotateAsPure from \"@babel/helper-annotate-as-pure\";\nimport { skipTransparentExprWrapperNodes } from \"@babel/helper-skip-transparent-expression-wrappers\";\n\ntype t = typeof t;\n\nconst ENUMS = new WeakMap<t.Identifier, PreviousEnumMembers>();\n\nconst buildEnumWrapper = template.expression(\n  `\n    (function (ID) {\n      ASSIGNMENTS;\n      return ID;\n    })(INIT)\n  `,\n);\n\nexport default function transpileEnum(\n  path: NodePath<t.TSEnumDeclaration>,\n  t: t,\n) {\n  const { node, parentPath } = path;\n\n  if (node.declare) {\n    path.remove();\n    return;\n  }\n\n  const name = node.id.name;\n  const { fill, data, isPure } = enumFill(path, t, node.id);\n\n  switch (parentPath.type) {\n    case \"BlockStatement\":\n    case \"ExportNamedDeclaration\":\n    case \"Program\": {\n      // todo: Consider exclude program with import/export\n      // && !path.parent.body.some(n => t.isImportDeclaration(n) || t.isExportDeclaration(n));\n      const isGlobal = t.isProgram(path.parent);\n      const isSeen = seen(parentPath);\n\n      let init: t.Expression = t.objectExpression([]);\n      if (isSeen || isGlobal) {\n        init = t.logicalExpression(\"||\", t.cloneNode(fill.ID), init);\n      }\n      const enumIIFE = buildEnumWrapper({ ...fill, INIT: init });\n      if (isPure) annotateAsPure(enumIIFE);\n\n      if (isSeen) {\n        const toReplace = parentPath.isExportDeclaration() ? parentPath : path;\n        toReplace.replaceWith(\n          t.expressionStatement(\n            t.assignmentExpression(\"=\", t.cloneNode(node.id), enumIIFE),\n          ),\n        );\n      } else {\n        path.scope.registerDeclaration(\n          path.replaceWith(\n            t.variableDeclaration(isGlobal ? \"var\" : \"let\", [\n              t.variableDeclarator(node.id, enumIIFE),\n            ]),\n          )[0],\n        );\n      }\n      ENUMS.set(path.scope.getBindingIdentifier(name), data);\n      break;\n    }\n\n    default:\n      throw new Error(`Unexpected enum parent '${path.parent.type}`);\n  }\n\n  function seen(parentPath: NodePath<t.Node>): boolean {\n    if (parentPath.isExportDeclaration()) {\n      return seen(parentPath.parentPath);\n    }\n\n    if (parentPath.getData(name)) {\n      return true;\n    } else {\n      parentPath.setData(name, true);\n      return false;\n    }\n  }\n}\n\nconst buildStringAssignment = template.statement(`\n  ENUM[\"NAME\"] = VALUE;\n`);\n\nconst buildNumericAssignment = template.statement(`\n  ENUM[ENUM[\"NAME\"] = VALUE] = \"NAME\";\n`);\n\nconst buildEnumMember = (isString: boolean, options: Record<string, unknown>) =>\n  (isString ? buildStringAssignment : buildNumericAssignment)(options);\n\n/**\n * Generates the statement that fills in the variable declared by the enum.\n * `(function (E) { ... assignments ... })(E || (E = {}));`\n */\nfunction enumFill(path: NodePath<t.TSEnumDeclaration>, t: t, id: t.Identifier) {\n  const { enumValues, data, isPure } = translateEnumValues(path, t);\n  const enumMembers: NodePath<t.TSEnumMember>[] = process.env.BABEL_8_BREAKING\n    ? // @ts-ignore(Babel 7 vs Babel 8) Babel 8 AST\n      path.get(\"body\").get(\"members\")\n    : path.get(\"members\");\n  const assignments = [];\n  for (let i = 0; i < enumMembers.length; i++) {\n    const [memberName, memberValue] = enumValues[i];\n    assignments.push(\n      t.inheritsComments(\n        buildEnumMember(isSyntacticallyString(memberValue), {\n          ENUM: t.cloneNode(id),\n          NAME: memberName,\n          VALUE: memberValue,\n        }),\n        enumMembers[i].node,\n      ),\n    );\n  }\n\n  return {\n    fill: {\n      ID: t.cloneNode(id),\n      ASSIGNMENTS: assignments,\n    },\n    data,\n    isPure,\n  };\n}\n\nexport function isSyntacticallyString(expr: t.Expression): boolean {\n  // @ts-ignore(Babel 7 vs Babel 8) Type 'Expression | Super' is not assignable to type 'Expression' in Babel 8\n  expr = skipTransparentExprWrapperNodes(expr);\n  switch (expr.type) {\n    case \"BinaryExpression\": {\n      const left = expr.left;\n      const right = expr.right;\n      return (\n        expr.operator === \"+\" &&\n        (isSyntacticallyString(left as t.Expression) ||\n          isSyntacticallyString(right))\n      );\n    }\n    case \"TemplateLiteral\":\n    case \"StringLiteral\":\n      return true;\n  }\n  return false;\n}\n\n/**\n * Maps the name of an enum member to its value.\n * We keep track of the previous enum members so you can write code like:\n *   enum E {\n *     X = 1 << 0,\n *     Y = 1 << 1,\n *     Z = X | Y,\n *   }\n */\ntype PreviousEnumMembers = Map<string, number | string>;\n\ntype EnumSelfReferenceVisitorState = {\n  seen: PreviousEnumMembers;\n  path: NodePath<t.TSEnumDeclaration>;\n  t: t;\n};\n\nfunction ReferencedIdentifier(\n  expr: NodePath<t.Identifier>,\n  state: EnumSelfReferenceVisitorState,\n) {\n  const { seen, path, t } = state;\n  const name = expr.node.name;\n\n  if (seen.has(name)) {\n    /* The name is declared inside enum:\n      enum Foo {\n        A,\n        B = (() => {\n          const A = 1;\n          return A;\n        })())\n      } */\n    if (process.env.BABEL_8_BREAKING) {\n      if (expr.scope.hasBinding(name, { upToScope: path.scope })) {\n        return;\n      }\n    } else {\n      for (\n        let curScope = expr.scope;\n        curScope !== path.scope;\n        curScope = curScope.parent\n      ) {\n        if (curScope.hasOwnBinding(name)) {\n          return;\n        }\n      }\n    }\n\n    expr.replaceWith(\n      t.memberExpression(t.cloneNode(path.node.id), t.cloneNode(expr.node)),\n    );\n    expr.skip();\n  }\n}\n\nconst enumSelfReferenceVisitor = {\n  ReferencedIdentifier,\n};\n\nexport function translateEnumValues(path: NodePath<t.TSEnumDeclaration>, t: t) {\n  const bindingIdentifier = path.scope.getBindingIdentifier(path.node.id.name);\n  const seen: PreviousEnumMembers = ENUMS.get(bindingIdentifier) ?? new Map();\n\n  // Start at -1 so the first enum member is its increment, 0.\n  let constValue: number | string | undefined = -1;\n  let lastName: string;\n  let isPure = true;\n\n  const enumMembers: NodePath<t.TSEnumMember>[] = process.env.BABEL_8_BREAKING\n    ? // @ts-ignore(Babel 7 vs Babel 8) Babel 8 AST\n      path.get(\"body\").get(\"members\")\n    : path.get(\"members\");\n\n  const enumValues: Array<[name: string, value: t.Expression]> =\n    enumMembers.map(memberPath => {\n      const member = memberPath.node;\n      const name = t.isIdentifier(member.id) ? member.id.name : member.id.value;\n      const initializerPath = memberPath.get(\"initializer\");\n      const initializer = member.initializer;\n      let value: t.Expression;\n      if (initializer) {\n        constValue = computeConstantValue(initializerPath, seen);\n        if (constValue !== undefined) {\n          seen.set(name, constValue);\n          assert(\n            typeof constValue === \"number\" || typeof constValue === \"string\",\n          );\n          // We do not use `t.valueToNode` because `Infinity`/`NaN` might refer\n          // to a local variable. Even 1/0\n          // Revisit once https://github.com/microsoft/TypeScript/issues/55091\n          // is fixed. Note: we will have to distinguish between actual\n          // infinities and reference  to non-infinite variables names Infinity.\n          if (constValue === Infinity || Number.isNaN(constValue)) {\n            value = t.identifier(String(constValue));\n          } else if (constValue === -Infinity) {\n            value = t.unaryExpression(\"-\", t.identifier(\"Infinity\"));\n          } else {\n            value = t.valueToNode(constValue);\n          }\n        } else {\n          isPure &&= initializerPath.isPure();\n\n          if (initializerPath.isReferencedIdentifier()) {\n            ReferencedIdentifier(initializerPath, {\n              t,\n              seen,\n              path,\n            });\n          } else {\n            initializerPath.traverse(enumSelfReferenceVisitor, {\n              t,\n              seen,\n              path,\n            });\n          }\n\n          value = initializerPath.node;\n          seen.set(name, undefined);\n        }\n      } else if (typeof constValue === \"number\") {\n        constValue += 1;\n        value = t.numericLiteral(constValue);\n        seen.set(name, constValue);\n      } else if (typeof constValue === \"string\") {\n        throw path.buildCodeFrameError(\"Enum member must have initializer.\");\n      } else {\n        // create dynamic initializer: 1 + ENUM[\"PREVIOUS\"]\n        const lastRef = t.memberExpression(\n          t.cloneNode(path.node.id),\n          t.stringLiteral(lastName),\n          true,\n        );\n        value = t.binaryExpression(\"+\", t.numericLiteral(1), lastRef);\n        seen.set(name, undefined);\n      }\n\n      lastName = name;\n      return [name, value];\n    });\n\n  return {\n    isPure,\n    data: seen,\n    enumValues,\n  };\n}\n\n// Based on the TypeScript repository's `computeConstantValue` in `checker.ts`.\nfunction computeConstantValue(\n  path: NodePath,\n  prevMembers?: PreviousEnumMembers,\n  seen: Set<t.Identifier> = new Set(),\n): number | string | undefined {\n  return evaluate(path);\n\n  function evaluate(path: NodePath): number | string | undefined {\n    const expr = path.node;\n    switch (expr.type) {\n      case \"MemberExpression\":\n        return evaluateRef(path, prevMembers, seen);\n      case \"StringLiteral\":\n        return expr.value;\n      case \"UnaryExpression\":\n        return evalUnaryExpression(path as NodePath<t.UnaryExpression>);\n      case \"BinaryExpression\":\n        return evalBinaryExpression(path as NodePath<t.BinaryExpression>);\n      case \"NumericLiteral\":\n        return expr.value;\n      case \"ParenthesizedExpression\":\n        return evaluate(path.get(\"expression\"));\n      case \"Identifier\":\n        return evaluateRef(path, prevMembers, seen);\n      case \"TemplateLiteral\": {\n        if (expr.quasis.length === 1) {\n          return expr.quasis[0].value.cooked;\n        }\n\n        const paths = (path as NodePath<t.TemplateLiteral>).get(\"expressions\");\n        const quasis = expr.quasis;\n        let str = \"\";\n\n        for (let i = 0; i < quasis.length; i++) {\n          str += quasis[i].value.cooked;\n\n          if (i + 1 < quasis.length) {\n            const value = evaluateRef(paths[i], prevMembers, seen);\n            if (value === undefined) return undefined;\n            str += value;\n          }\n        }\n        return str;\n      }\n      default:\n        return undefined;\n    }\n  }\n\n  function evaluateRef(\n    path: NodePath,\n    prevMembers: PreviousEnumMembers,\n    seen: Set<t.Identifier>,\n  ): number | string | undefined {\n    if (path.isMemberExpression()) {\n      const expr = path.node;\n\n      const obj = expr.object;\n      const prop = expr.property;\n      if (\n        !t.isIdentifier(obj) ||\n        (expr.computed ? !t.isStringLiteral(prop) : !t.isIdentifier(prop))\n      ) {\n        return;\n      }\n      const bindingIdentifier = path.scope.getBindingIdentifier(obj.name);\n      const data = ENUMS.get(bindingIdentifier);\n      if (!data) return;\n      // @ts-expect-error checked above\n      return data.get(prop.computed ? prop.value : prop.name);\n    } else if (path.isIdentifier()) {\n      const name = path.node.name;\n\n      if ([\"Infinity\", \"NaN\"].includes(name)) {\n        return Number(name);\n      }\n\n      let value = prevMembers?.get(name);\n      if (value !== undefined) {\n        return value;\n      }\n      if (prevMembers?.has(name)) {\n        // prevMembers contains name => undefined. This means the value couldn't be pre-computed.\n        return undefined;\n      }\n\n      if (seen.has(path.node)) return;\n      seen.add(path.node);\n\n      value = computeConstantValue(path.resolve(), prevMembers, seen);\n      return value;\n    }\n  }\n\n  function evalUnaryExpression(\n    path: NodePath<t.UnaryExpression>,\n  ): number | string | undefined {\n    const value = evaluate(path.get(\"argument\"));\n    if (value === undefined) {\n      return undefined;\n    }\n\n    switch (path.node.operator) {\n      case \"+\":\n        return value;\n      case \"-\":\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-unary-minus\n        return -value;\n      case \"~\":\n        return ~value;\n      default:\n        return undefined;\n    }\n  }\n\n  function evalBinaryExpression(\n    path: NodePath<t.BinaryExpression>,\n  ): number | string | undefined {\n    const left = evaluate(path.get(\"left\")) as any;\n    if (left === undefined) {\n      return undefined;\n    }\n    const right = evaluate(path.get(\"right\")) as any;\n    if (right === undefined) {\n      return undefined;\n    }\n\n    switch (path.node.operator) {\n      case \"|\":\n        return left | right;\n      case \"&\":\n        return left & right;\n      case \">>\":\n        return left >> right;\n      case \">>>\":\n        return left >>> right;\n      case \"<<\":\n        return left << right;\n      case \"^\":\n        return left ^ right;\n      case \"*\":\n        return left * right;\n      case \"/\":\n        return left / right;\n      case \"+\":\n        return left + right;\n      case \"-\":\n        return left - right;\n      case \"%\":\n        return left % right;\n      case \"**\":\n        return left ** right;\n      default:\n        return undefined;\n    }\n  }\n}\n"], "mappings": ";;;;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,qBAAA,GAAAF,OAAA;AACA,IAAAG,wCAAA,GAAAH,OAAA;AAIA,MAAMI,KAAK,GAAG,IAAIC,OAAO,CAAoC,CAAC;AAE9D,MAAMC,gBAAgB,GAAGC,cAAQ,CAACC,UAAU,CAC1C;AACF;AACA;AACA;AACA;AACA,GACA,CAAC;AAEc,SAASC,aAAaA,CACnCC,IAAmC,EACnCC,CAAI,EACJ;EACA,MAAM;IAAEC,IAAI;IAAEC;EAAW,CAAC,GAAGH,IAAI;EAEjC,IAAIE,IAAI,CAACE,OAAO,EAAE;IAChBJ,IAAI,CAACK,MAAM,CAAC,CAAC;IACb;EACF;EAEA,MAAMC,IAAI,GAAGJ,IAAI,CAACK,EAAE,CAACD,IAAI;EACzB,MAAM;IAAEE,IAAI;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGC,QAAQ,CAACX,IAAI,EAAEC,CAAC,EAAEC,IAAI,CAACK,EAAE,CAAC;EAEzD,QAAQJ,UAAU,CAACS,IAAI;IACrB,KAAK,gBAAgB;IACrB,KAAK,wBAAwB;IAC7B,KAAK,SAAS;MAAE;QAGd,MAAMC,QAAQ,GAAGZ,CAAC,CAACa,SAAS,CAACd,IAAI,CAACe,MAAM,CAAC;QACzC,MAAMC,MAAM,GAAGC,IAAI,CAACd,UAAU,CAAC;QAE/B,IAAIe,IAAkB,GAAGjB,CAAC,CAACkB,gBAAgB,CAAC,EAAE,CAAC;QAC/C,IAAIH,MAAM,IAAIH,QAAQ,EAAE;UACtBK,IAAI,GAAGjB,CAAC,CAACmB,iBAAiB,CAAC,IAAI,EAAEnB,CAAC,CAACoB,SAAS,CAACb,IAAI,CAACc,EAAE,CAAC,EAAEJ,IAAI,CAAC;QAC9D;QACA,MAAMK,QAAQ,GAAG3B,gBAAgB,CAAA4B,MAAA,CAAAC,MAAA,KAAMjB,IAAI;UAAEkB,IAAI,EAAER;QAAI,EAAE,CAAC;QAC1D,IAAIR,MAAM,EAAE,IAAAiB,6BAAc,EAACJ,QAAQ,CAAC;QAEpC,IAAIP,MAAM,EAAE;UACV,MAAMY,SAAS,GAAGzB,UAAU,CAAC0B,mBAAmB,CAAC,CAAC,GAAG1B,UAAU,GAAGH,IAAI;UACtE4B,SAAS,CAACE,WAAW,CACnB7B,CAAC,CAAC8B,mBAAmB,CACnB9B,CAAC,CAAC+B,oBAAoB,CAAC,GAAG,EAAE/B,CAAC,CAACoB,SAAS,CAACnB,IAAI,CAACK,EAAE,CAAC,EAAEgB,QAAQ,CAC5D,CACF,CAAC;QACH,CAAC,MAAM;UACLvB,IAAI,CAACiC,KAAK,CAACC,mBAAmB,CAC5BlC,IAAI,CAAC8B,WAAW,CACd7B,CAAC,CAACkC,mBAAmB,CAACtB,QAAQ,GAAG,KAAK,GAAG,KAAK,EAAE,CAC9CZ,CAAC,CAACmC,kBAAkB,CAAClC,IAAI,CAACK,EAAE,EAAEgB,QAAQ,CAAC,CACxC,CACH,CAAC,CAAC,CAAC,CACL,CAAC;QACH;QACA7B,KAAK,CAAC2C,GAAG,CAACrC,IAAI,CAACiC,KAAK,CAACK,oBAAoB,CAAChC,IAAI,CAAC,EAAEG,IAAI,CAAC;QACtD;MACF;IAEA;MACE,MAAM,IAAI8B,KAAK,CAAC,2BAA2BvC,IAAI,CAACe,MAAM,CAACH,IAAI,EAAE,CAAC;EAClE;EAEA,SAASK,IAAIA,CAACd,UAA4B,EAAW;IACnD,IAAIA,UAAU,CAAC0B,mBAAmB,CAAC,CAAC,EAAE;MACpC,OAAOZ,IAAI,CAACd,UAAU,CAACA,UAAU,CAAC;IACpC;IAEA,IAAIA,UAAU,CAACqC,OAAO,CAAClC,IAAI,CAAC,EAAE;MAC5B,OAAO,IAAI;IACb,CAAC,MAAM;MACLH,UAAU,CAACsC,OAAO,CAACnC,IAAI,EAAE,IAAI,CAAC;MAC9B,OAAO,KAAK;IACd;EACF;AACF;AAEA,MAAMoC,qBAAqB,GAAG7C,cAAQ,CAAC8C,SAAS,CAAC;AACjD;AACA,CAAC,CAAC;AAEF,MAAMC,sBAAsB,GAAG/C,cAAQ,CAAC8C,SAAS,CAAC;AAClD;AACA,CAAC,CAAC;AAEF,MAAME,eAAe,GAAGA,CAACC,QAAiB,EAAEC,OAAgC,KAC1E,CAACD,QAAQ,GAAGJ,qBAAqB,GAAGE,sBAAsB,EAAEG,OAAO,CAAC;AAMtE,SAASpC,QAAQA,CAACX,IAAmC,EAAEC,CAAI,EAAEM,EAAgB,EAAE;EAC7E,MAAM;IAAEyC,UAAU;IAAEvC,IAAI;IAAEC;EAAO,CAAC,GAAGuC,mBAAmB,CAACjD,IAAI,EAAEC,CAAC,CAAC;EACjE,MAAMiD,WAAuC,GAGzClD,IAAI,CAACmD,GAAG,CAAC,SAAS,CAAC;EACvB,MAAMC,WAAW,GAAG,EAAE;EACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,WAAW,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3C,MAAM,CAACE,UAAU,EAAEC,WAAW,CAAC,GAAGR,UAAU,CAACK,CAAC,CAAC;IAC/CD,WAAW,CAACK,IAAI,CACdxD,CAAC,CAACyD,gBAAgB,CAChBb,eAAe,CAACc,qBAAqB,CAACH,WAAW,CAAC,EAAE;MAClDI,IAAI,EAAE3D,CAAC,CAACoB,SAAS,CAACd,EAAE,CAAC;MACrBsD,IAAI,EAAEN,UAAU;MAChBO,KAAK,EAAEN;IACT,CAAC,CAAC,EACFN,WAAW,CAACG,CAAC,CAAC,CAACnD,IACjB,CACF,CAAC;EACH;EAEA,OAAO;IACLM,IAAI,EAAE;MACJc,EAAE,EAAErB,CAAC,CAACoB,SAAS,CAACd,EAAE,CAAC;MACnBwD,WAAW,EAAEX;IACf,CAAC;IACD3C,IAAI;IACJC;EACF,CAAC;AACH;AAEO,SAASiD,qBAAqBA,CAACK,IAAkB,EAAW;EAEjEA,IAAI,GAAG,IAAAC,wEAA+B,EAACD,IAAI,CAAC;EAC5C,QAAQA,IAAI,CAACpD,IAAI;IACf,KAAK,kBAAkB;MAAE;QACvB,MAAMsD,IAAI,GAAGF,IAAI,CAACE,IAAI;QACtB,MAAMC,KAAK,GAAGH,IAAI,CAACG,KAAK;QACxB,OACEH,IAAI,CAACI,QAAQ,KAAK,GAAG,KACpBT,qBAAqB,CAACO,IAAoB,CAAC,IAC1CP,qBAAqB,CAACQ,KAAK,CAAC,CAAC;MAEnC;IACA,KAAK,iBAAiB;IACtB,KAAK,eAAe;MAClB,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AACd;AAmBA,SAASE,oBAAoBA,CAC3BL,IAA4B,EAC5BM,KAAoC,EACpC;EACA,MAAM;IAAErD,IAAI;IAAEjB,IAAI;IAAEC;EAAE,CAAC,GAAGqE,KAAK;EAC/B,MAAMhE,IAAI,GAAG0D,IAAI,CAAC9D,IAAI,CAACI,IAAI;EAE3B,IAAIW,IAAI,CAACsD,GAAG,CAACjE,IAAI,CAAC,EAAE;IAaX;MACL,KACE,IAAIkE,QAAQ,GAAGR,IAAI,CAAC/B,KAAK,EACzBuC,QAAQ,KAAKxE,IAAI,CAACiC,KAAK,EACvBuC,QAAQ,GAAGA,QAAQ,CAACzD,MAAM,EAC1B;QACA,IAAIyD,QAAQ,CAACC,aAAa,CAACnE,IAAI,CAAC,EAAE;UAChC;QACF;MACF;IACF;IAEA0D,IAAI,CAAClC,WAAW,CACd7B,CAAC,CAACyE,gBAAgB,CAACzE,CAAC,CAACoB,SAAS,CAACrB,IAAI,CAACE,IAAI,CAACK,EAAE,CAAC,EAAEN,CAAC,CAACoB,SAAS,CAAC2C,IAAI,CAAC9D,IAAI,CAAC,CACtE,CAAC;IACD8D,IAAI,CAACW,IAAI,CAAC,CAAC;EACb;AACF;AAEA,MAAMC,wBAAwB,GAAG;EAC/BP;AACF,CAAC;AAEM,SAASpB,mBAAmBA,CAACjD,IAAmC,EAAEC,CAAI,EAAE;EAAA,IAAA4E,UAAA;EAC7E,MAAMC,iBAAiB,GAAG9E,IAAI,CAACiC,KAAK,CAACK,oBAAoB,CAACtC,IAAI,CAACE,IAAI,CAACK,EAAE,CAACD,IAAI,CAAC;EAC5E,MAAMW,IAAyB,IAAA4D,UAAA,GAAGnF,KAAK,CAACyD,GAAG,CAAC2B,iBAAiB,CAAC,YAAAD,UAAA,GAAI,IAAIE,GAAG,CAAC,CAAC;EAG3E,IAAIC,UAAuC,GAAG,CAAC,CAAC;EAChD,IAAIC,QAAgB;EACpB,IAAIvE,MAAM,GAAG,IAAI;EAEjB,MAAMwC,WAAuC,GAGzClD,IAAI,CAACmD,GAAG,CAAC,SAAS,CAAC;EAEvB,MAAMH,UAAsD,GAC1DE,WAAW,CAACgC,GAAG,CAACC,UAAU,IAAI;IAC5B,MAAMC,MAAM,GAAGD,UAAU,CAACjF,IAAI;IAC9B,MAAMI,IAAI,GAAGL,CAAC,CAACoF,YAAY,CAACD,MAAM,CAAC7E,EAAE,CAAC,GAAG6E,MAAM,CAAC7E,EAAE,CAACD,IAAI,GAAG8E,MAAM,CAAC7E,EAAE,CAAC+E,KAAK;IACzE,MAAMC,eAAe,GAAGJ,UAAU,CAAChC,GAAG,CAAC,aAAa,CAAC;IACrD,MAAMqC,WAAW,GAAGJ,MAAM,CAACI,WAAW;IACtC,IAAIF,KAAmB;IACvB,IAAIE,WAAW,EAAE;MACfR,UAAU,GAAGS,oBAAoB,CAACF,eAAe,EAAEtE,IAAI,CAAC;MACxD,IAAI+D,UAAU,KAAKU,SAAS,EAAE;QAC5BzE,IAAI,CAACoB,GAAG,CAAC/B,IAAI,EAAE0E,UAAU,CAAC;QAC1BW,OAAM,CACJ,OAAOX,UAAU,KAAK,QAAQ,IAAI,OAAOA,UAAU,KAAK,QAC1D,CAAC;QAMD,IAAIA,UAAU,KAAKY,QAAQ,IAAIC,MAAM,CAACC,KAAK,CAACd,UAAU,CAAC,EAAE;UACvDM,KAAK,GAAGrF,CAAC,CAAC8F,UAAU,CAACC,MAAM,CAAChB,UAAU,CAAC,CAAC;QAC1C,CAAC,MAAM,IAAIA,UAAU,KAAK,CAACY,QAAQ,EAAE;UACnCN,KAAK,GAAGrF,CAAC,CAACgG,eAAe,CAAC,GAAG,EAAEhG,CAAC,CAAC8F,UAAU,CAAC,UAAU,CAAC,CAAC;QAC1D,CAAC,MAAM;UACLT,KAAK,GAAGrF,CAAC,CAACiG,WAAW,CAAClB,UAAU,CAAC;QACnC;MACF,CAAC,MAAM;QACLtE,MAAM,KAANA,MAAM,GAAK6E,eAAe,CAAC7E,MAAM,CAAC,CAAC;QAEnC,IAAI6E,eAAe,CAACY,sBAAsB,CAAC,CAAC,EAAE;UAC5C9B,oBAAoB,CAACkB,eAAe,EAAE;YACpCtF,CAAC;YACDgB,IAAI;YACJjB;UACF,CAAC,CAAC;QACJ,CAAC,MAAM;UACLuF,eAAe,CAACa,QAAQ,CAACxB,wBAAwB,EAAE;YACjD3E,CAAC;YACDgB,IAAI;YACJjB;UACF,CAAC,CAAC;QACJ;QAEAsF,KAAK,GAAGC,eAAe,CAACrF,IAAI;QAC5Be,IAAI,CAACoB,GAAG,CAAC/B,IAAI,EAAEoF,SAAS,CAAC;MAC3B;IACF,CAAC,MAAM,IAAI,OAAOV,UAAU,KAAK,QAAQ,EAAE;MACzCA,UAAU,IAAI,CAAC;MACfM,KAAK,GAAGrF,CAAC,CAACoG,cAAc,CAACrB,UAAU,CAAC;MACpC/D,IAAI,CAACoB,GAAG,CAAC/B,IAAI,EAAE0E,UAAU,CAAC;IAC5B,CAAC,MAAM,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;MACzC,MAAMhF,IAAI,CAACsG,mBAAmB,CAAC,oCAAoC,CAAC;IACtE,CAAC,MAAM;MAEL,MAAMC,OAAO,GAAGtG,CAAC,CAACyE,gBAAgB,CAChCzE,CAAC,CAACoB,SAAS,CAACrB,IAAI,CAACE,IAAI,CAACK,EAAE,CAAC,EACzBN,CAAC,CAACuG,aAAa,CAACvB,QAAQ,CAAC,EACzB,IACF,CAAC;MACDK,KAAK,GAAGrF,CAAC,CAACwG,gBAAgB,CAAC,GAAG,EAAExG,CAAC,CAACoG,cAAc,CAAC,CAAC,CAAC,EAAEE,OAAO,CAAC;MAC7DtF,IAAI,CAACoB,GAAG,CAAC/B,IAAI,EAAEoF,SAAS,CAAC;IAC3B;IAEAT,QAAQ,GAAG3E,IAAI;IACf,OAAO,CAACA,IAAI,EAAEgF,KAAK,CAAC;EACtB,CAAC,CAAC;EAEJ,OAAO;IACL5E,MAAM;IACND,IAAI,EAAEQ,IAAI;IACV+B;EACF,CAAC;AACH;AAGA,SAASyC,oBAAoBA,CAC3BzF,IAAc,EACd0G,WAAiC,EACjCzF,IAAuB,GAAG,IAAI0F,GAAG,CAAC,CAAC,EACN;EAC7B,OAAOC,QAAQ,CAAC5G,IAAI,CAAC;EAErB,SAAS4G,QAAQA,CAAC5G,IAAc,EAA+B;IAC7D,MAAMgE,IAAI,GAAGhE,IAAI,CAACE,IAAI;IACtB,QAAQ8D,IAAI,CAACpD,IAAI;MACf,KAAK,kBAAkB;QACrB,OAAOiG,WAAW,CAAC7G,IAAI,EAAE0G,WAAW,EAAEzF,IAAI,CAAC;MAC7C,KAAK,eAAe;QAClB,OAAO+C,IAAI,CAACsB,KAAK;MACnB,KAAK,iBAAiB;QACpB,OAAOwB,mBAAmB,CAAC9G,IAAmC,CAAC;MACjE,KAAK,kBAAkB;QACrB,OAAO+G,oBAAoB,CAAC/G,IAAoC,CAAC;MACnE,KAAK,gBAAgB;QACnB,OAAOgE,IAAI,CAACsB,KAAK;MACnB,KAAK,yBAAyB;QAC5B,OAAOsB,QAAQ,CAAC5G,IAAI,CAACmD,GAAG,CAAC,YAAY,CAAC,CAAC;MACzC,KAAK,YAAY;QACf,OAAO0D,WAAW,CAAC7G,IAAI,EAAE0G,WAAW,EAAEzF,IAAI,CAAC;MAC7C,KAAK,iBAAiB;QAAE;UACtB,IAAI+C,IAAI,CAACgD,MAAM,CAAC1D,MAAM,KAAK,CAAC,EAAE;YAC5B,OAAOU,IAAI,CAACgD,MAAM,CAAC,CAAC,CAAC,CAAC1B,KAAK,CAAC2B,MAAM;UACpC;UAEA,MAAMC,KAAK,GAAIlH,IAAI,CAAiCmD,GAAG,CAAC,aAAa,CAAC;UACtE,MAAM6D,MAAM,GAAGhD,IAAI,CAACgD,MAAM;UAC1B,IAAIG,GAAG,GAAG,EAAE;UAEZ,KAAK,IAAI9D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2D,MAAM,CAAC1D,MAAM,EAAED,CAAC,EAAE,EAAE;YACtC8D,GAAG,IAAIH,MAAM,CAAC3D,CAAC,CAAC,CAACiC,KAAK,CAAC2B,MAAM;YAE7B,IAAI5D,CAAC,GAAG,CAAC,GAAG2D,MAAM,CAAC1D,MAAM,EAAE;cACzB,MAAMgC,KAAK,GAAGuB,WAAW,CAACK,KAAK,CAAC7D,CAAC,CAAC,EAAEqD,WAAW,EAAEzF,IAAI,CAAC;cACtD,IAAIqE,KAAK,KAAKI,SAAS,EAAE,OAAOA,SAAS;cACzCyB,GAAG,IAAI7B,KAAK;YACd;UACF;UACA,OAAO6B,GAAG;QACZ;MACA;QACE,OAAOzB,SAAS;IACpB;EACF;EAEA,SAASmB,WAAWA,CAClB7G,IAAc,EACd0G,WAAgC,EAChCzF,IAAuB,EACM;IAC7B,IAAIjB,IAAI,CAACoH,kBAAkB,CAAC,CAAC,EAAE;MAC7B,MAAMpD,IAAI,GAAGhE,IAAI,CAACE,IAAI;MAEtB,MAAMmH,GAAG,GAAGrD,IAAI,CAACsD,MAAM;MACvB,MAAMC,IAAI,GAAGvD,IAAI,CAACwD,QAAQ;MAC1B,IACE,CAACvH,WAAC,CAACoF,YAAY,CAACgC,GAAG,CAAC,KACnBrD,IAAI,CAACyD,QAAQ,GAAG,CAACxH,WAAC,CAACyH,eAAe,CAACH,IAAI,CAAC,GAAG,CAACtH,WAAC,CAACoF,YAAY,CAACkC,IAAI,CAAC,CAAC,EAClE;QACA;MACF;MACA,MAAMzC,iBAAiB,GAAG9E,IAAI,CAACiC,KAAK,CAACK,oBAAoB,CAAC+E,GAAG,CAAC/G,IAAI,CAAC;MACnE,MAAMG,IAAI,GAAGf,KAAK,CAACyD,GAAG,CAAC2B,iBAAiB,CAAC;MACzC,IAAI,CAACrE,IAAI,EAAE;MAEX,OAAOA,IAAI,CAAC0C,GAAG,CAACoE,IAAI,CAACE,QAAQ,GAAGF,IAAI,CAACjC,KAAK,GAAGiC,IAAI,CAACjH,IAAI,CAAC;IACzD,CAAC,MAAM,IAAIN,IAAI,CAACqF,YAAY,CAAC,CAAC,EAAE;MAC9B,MAAM/E,IAAI,GAAGN,IAAI,CAACE,IAAI,CAACI,IAAI;MAE3B,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAACqH,QAAQ,CAACrH,IAAI,CAAC,EAAE;QACtC,OAAOuF,MAAM,CAACvF,IAAI,CAAC;MACrB;MAEA,IAAIgF,KAAK,GAAGoB,WAAW,oBAAXA,WAAW,CAAEvD,GAAG,CAAC7C,IAAI,CAAC;MAClC,IAAIgF,KAAK,KAAKI,SAAS,EAAE;QACvB,OAAOJ,KAAK;MACd;MACA,IAAIoB,WAAW,YAAXA,WAAW,CAAEnC,GAAG,CAACjE,IAAI,CAAC,EAAE;QAE1B,OAAOoF,SAAS;MAClB;MAEA,IAAIzE,IAAI,CAACsD,GAAG,CAACvE,IAAI,CAACE,IAAI,CAAC,EAAE;MACzBe,IAAI,CAAC2G,GAAG,CAAC5H,IAAI,CAACE,IAAI,CAAC;MAEnBoF,KAAK,GAAGG,oBAAoB,CAACzF,IAAI,CAAC6H,OAAO,CAAC,CAAC,EAAEnB,WAAW,EAAEzF,IAAI,CAAC;MAC/D,OAAOqE,KAAK;IACd;EACF;EAEA,SAASwB,mBAAmBA,CAC1B9G,IAAiC,EACJ;IAC7B,MAAMsF,KAAK,GAAGsB,QAAQ,CAAC5G,IAAI,CAACmD,GAAG,CAAC,UAAU,CAAC,CAAC;IAC5C,IAAImC,KAAK,KAAKI,SAAS,EAAE;MACvB,OAAOA,SAAS;IAClB;IAEA,QAAQ1F,IAAI,CAACE,IAAI,CAACkE,QAAQ;MACxB,KAAK,GAAG;QACN,OAAOkB,KAAK;MACd,KAAK,GAAG;QAEN,OAAO,CAACA,KAAK;MACf,KAAK,GAAG;QACN,OAAO,CAACA,KAAK;MACf;QACE,OAAOI,SAAS;IACpB;EACF;EAEA,SAASqB,oBAAoBA,CAC3B/G,IAAkC,EACL;IAC7B,MAAMkE,IAAI,GAAG0C,QAAQ,CAAC5G,IAAI,CAACmD,GAAG,CAAC,MAAM,CAAC,CAAQ;IAC9C,IAAIe,IAAI,KAAKwB,SAAS,EAAE;MACtB,OAAOA,SAAS;IAClB;IACA,MAAMvB,KAAK,GAAGyC,QAAQ,CAAC5G,IAAI,CAACmD,GAAG,CAAC,OAAO,CAAC,CAAQ;IAChD,IAAIgB,KAAK,KAAKuB,SAAS,EAAE;MACvB,OAAOA,SAAS;IAClB;IAEA,QAAQ1F,IAAI,CAACE,IAAI,CAACkE,QAAQ;MACxB,KAAK,GAAG;QACN,OAAOF,IAAI,GAAGC,KAAK;MACrB,KAAK,GAAG;QACN,OAAOD,IAAI,GAAGC,KAAK;MACrB,KAAK,IAAI;QACP,OAAOD,IAAI,IAAIC,KAAK;MACtB,KAAK,KAAK;QACR,OAAOD,IAAI,KAAKC,KAAK;MACvB,KAAK,IAAI;QACP,OAAOD,IAAI,IAAIC,KAAK;MACtB,KAAK,GAAG;QACN,OAAOD,IAAI,GAAGC,KAAK;MACrB,KAAK,GAAG;QACN,OAAOD,IAAI,GAAGC,KAAK;MACrB,KAAK,GAAG;QACN,OAAOD,IAAI,GAAGC,KAAK;MACrB,KAAK,GAAG;QACN,OAAOD,IAAI,GAAGC,KAAK;MACrB,KAAK,GAAG;QACN,OAAOD,IAAI,GAAGC,KAAK;MACrB,KAAK,GAAG;QACN,OAAOD,IAAI,GAAGC,KAAK;MACrB,KAAK,IAAI;QACP,OAAA2D,IAAA,CAAAC,GAAA,CAAO7D,IAAI,EAAIC,KAAK;MACtB;QACE,OAAOuB,SAAS;IACpB;EACF;AACF", "ignoreList": []}