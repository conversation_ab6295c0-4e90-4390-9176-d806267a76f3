{"version": 3, "names": ["_OverloadYield", "require", "_regenerator", "_regeneratorAsync", "_regeneratorAsyncGen", "_regeneratorAsyncIterator", "_regeneratorKeys", "_regeneratorValues", "_regeneratorRuntime", "r", "regenerator", "gen", "m", "GeneratorFunctionPrototype", "Object", "getPrototypeOf", "__proto__", "GeneratorFunction", "constructor", "isGeneratorFunction", "gen<PERSON>un", "ctor", "displayName", "name", "abruptMap", "throw", "return", "break", "continue", "wrapInnerFn", "innerFn", "compatContext", "callSyncState", "context", "stop", "a", "catch", "v", "abrupt", "type", "arg", "<PERSON><PERSON><PERSON>", "iterable", "resultName", "nextLoc", "d", "values", "finish", "finallyLoc", "f", "fn", "a1", "a2", "p", "prev", "n", "next", "undefined", "sent", "call", "exports", "default", "wrap", "outerFn", "self", "tryLocsList", "w", "reverse", "mark", "awrap", "value", "kind", "OverloadYield", "AsyncIterator", "async", "PromiseImpl", "asyncGen", "keys"], "sources": ["../../src/helpers/regeneratorRuntime.ts"], "sourcesContent": ["/* @minVersion 7.18.0 */\n/* @mangleFns */\n/* @onlyBabel7 */\n\nimport OverloadYield from \"./OverloadYield.ts\";\nimport regenerator from \"./regenerator.ts\";\nimport async from \"./regeneratorAsync.ts\";\nimport asyncGen from \"./regeneratorAsyncGen.ts\";\nimport AsyncIterator from \"./regeneratorAsyncIterator.ts\";\nimport keys from \"./regeneratorKeys.ts\";\nimport values from \"./regeneratorValues.ts\";\n\ntype CompatContext = {\n  prev?: number;\n  next?: number;\n  sent?: any;\n\n  stop(): any;\n  abrupt(type: \"throw\" | \"break\" | \"continue\" | \"return\", arg: any): any;\n  finish(finallyLoc: number): any;\n  catch(tryLoc: number): any;\n  delegateYield(iterable: any, resultName: `t${number}`, nextLoc: number): any;\n\n  resultName?: `t${number}` | undefined;\n\n  [key: `t${number}`]: any;\n};\ntype CompatInnerFn = (this: unknown, context: CompatContext) => unknown;\n\nexport default function /* @no-mangle */ _regeneratorRuntime() {\n  \"use strict\";\n\n  var r = regenerator();\n\n  type InnerFn = Parameters<typeof r.w>[0];\n  type Context = Parameters<InnerFn>[0];\n\n  var gen = r.m(_regeneratorRuntime);\n  var GeneratorFunctionPrototype = Object.getPrototypeOf\n    ? Object.getPrototypeOf(gen)\n    : (gen as any).__proto__;\n  var GeneratorFunction = GeneratorFunctionPrototype.constructor;\n\n  function isGeneratorFunction(genFun: any) {\n    var ctor = typeof genFun === \"function\" && genFun.constructor;\n    return ctor\n      ? ctor === GeneratorFunction ||\n          // For the native GeneratorFunction constructor, the best we can\n          // do is to check its .name property.\n          (ctor.displayName || ctor.name) === \"GeneratorFunction\"\n      : false;\n  }\n\n  var abruptMap = {\n    throw: 1,\n    return: 2,\n    break: 3,\n    continue: 3,\n  };\n\n  function wrapInnerFn(innerFn: CompatInnerFn): InnerFn {\n    var compatContext: CompatContext;\n    var callSyncState: <A1, A2, R>(\n      fn: (a: A1, b: A2) => R,\n      a1: A1,\n      a2?: A2,\n    ) => R;\n    return function (context: Context) {\n      if (!compatContext) {\n        // Shim the old context shape on top of the new one.\n        compatContext = {\n          stop: function () {\n            return callSyncState(context.a, 2);\n          },\n          catch: function () {\n            return context.v;\n          },\n          abrupt: function (type, arg) {\n            return callSyncState(context.a, abruptMap[type], arg);\n          },\n          delegateYield: function (iterable, resultName, nextLoc) {\n            compatContext.resultName = resultName;\n            return callSyncState(context.d, values(iterable), nextLoc);\n          },\n          finish: function (finallyLoc) {\n            return callSyncState(context.f, finallyLoc);\n          },\n        };\n        callSyncState = function (fn, a1, a2) {\n          context.p = compatContext.prev!;\n          context.n = compatContext.next!;\n          try {\n            return fn(a1, a2!);\n          } finally {\n            compatContext.next = context.n;\n          }\n        };\n      }\n      if (compatContext.resultName) {\n        compatContext[compatContext.resultName] = context.v;\n        compatContext.resultName = undefined;\n      }\n      compatContext.sent = context.v;\n      compatContext.next = context.n;\n      try {\n        return innerFn.call(this, compatContext);\n      } finally {\n        context.p = compatContext.prev!;\n        context.n = compatContext.next;\n      }\n    };\n  }\n\n  // @ts-expect-error explicit function assignment\n  return (_regeneratorRuntime = function () {\n    return {\n      wrap: function (\n        innerFn: CompatInnerFn,\n        outerFn: Parameters<typeof r.w>[1],\n        self: Parameters<typeof r.w>[2],\n        tryLocsList: Parameters<typeof r.w>[3],\n      ) {\n        return r.w(\n          wrapInnerFn(innerFn),\n          outerFn,\n          self,\n          tryLocsList && tryLocsList.reverse(),\n        );\n      },\n      isGeneratorFunction: isGeneratorFunction,\n      mark: r.m,\n      awrap: function (value: any, kind: any) {\n        return new OverloadYield(value, kind);\n      },\n      AsyncIterator: AsyncIterator,\n      async: function (\n        innerFn: CompatInnerFn,\n        outerFn: Function,\n        self: any,\n        tryLocsList: any[],\n        PromiseImpl?: PromiseConstructor,\n      ) {\n        return (isGeneratorFunction(outerFn) ? asyncGen : async)(\n          wrapInnerFn(innerFn),\n          outerFn,\n          self,\n          tryLocsList,\n          PromiseImpl,\n        );\n      },\n      keys: keys,\n      values: values,\n    };\n  })();\n}\n"], "mappings": ";;;;;;AAIA,IAAAA,cAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AACA,IAAAE,iBAAA,GAAAF,OAAA;AACA,IAAAG,oBAAA,GAAAH,OAAA;AACA,IAAAI,yBAAA,GAAAJ,OAAA;AACA,IAAAK,gBAAA,GAAAL,OAAA;AACA,IAAAM,kBAAA,GAAAN,OAAA;AAmBe,SAA0BO,mBAAmBA,CAAA,EAAG;EAC7D,YAAY;;EAEZ,IAAIC,CAAC,GAAG,IAAAC,oBAAW,EAAC,CAAC;EAKrB,IAAIC,GAAG,GAAGF,CAAC,CAACG,CAAC,CAACJ,mBAAmB,CAAC;EAClC,IAAIK,0BAA0B,GAAGC,MAAM,CAACC,cAAc,GAClDD,MAAM,CAACC,cAAc,CAACJ,GAAG,CAAC,GACzBA,GAAG,CAASK,SAAS;EAC1B,IAAIC,iBAAiB,GAAGJ,0BAA0B,CAACK,WAAW;EAE9D,SAASC,mBAAmBA,CAACC,MAAW,EAAE;IACxC,IAAIC,IAAI,GAAG,OAAOD,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACF,WAAW;IAC7D,OAAOG,IAAI,GACPA,IAAI,KAAKJ,iBAAiB,IAGxB,CAACI,IAAI,CAACC,WAAW,IAAID,IAAI,CAACE,IAAI,MAAM,mBAAmB,GACzD,KAAK;EACX;EAEA,IAAIC,SAAS,GAAG;IACdC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE;EACZ,CAAC;EAED,SAASC,WAAWA,CAACC,OAAsB,EAAW;IACpD,IAAIC,aAA4B;IAChC,IAAIC,aAIE;IACN,OAAO,UAAUC,OAAgB,EAAE;MACjC,IAAI,CAACF,aAAa,EAAE;QAElBA,aAAa,GAAG;UACdG,IAAI,EAAE,SAAAA,CAAA,EAAY;YAChB,OAAOF,aAAa,CAACC,OAAO,CAACE,CAAC,EAAE,CAAC,CAAC;UACpC,CAAC;UACDC,KAAK,EAAE,SAAAA,CAAA,EAAY;YACjB,OAAOH,OAAO,CAACI,CAAC;UAClB,CAAC;UACDC,MAAM,EAAE,SAAAA,CAAUC,IAAI,EAAEC,GAAG,EAAE;YAC3B,OAAOR,aAAa,CAACC,OAAO,CAACE,CAAC,EAAEX,SAAS,CAACe,IAAI,CAAC,EAAEC,GAAG,CAAC;UACvD,CAAC;UACDC,aAAa,EAAE,SAAAA,CAAUC,QAAQ,EAAEC,UAAU,EAAEC,OAAO,EAAE;YACtDb,aAAa,CAACY,UAAU,GAAGA,UAAU;YACrC,OAAOX,aAAa,CAACC,OAAO,CAACY,CAAC,EAAE,IAAAC,0BAAM,EAACJ,QAAQ,CAAC,EAAEE,OAAO,CAAC;UAC5D,CAAC;UACDG,MAAM,EAAE,SAAAA,CAAUC,UAAU,EAAE;YAC5B,OAAOhB,aAAa,CAACC,OAAO,CAACgB,CAAC,EAAED,UAAU,CAAC;UAC7C;QACF,CAAC;QACDhB,aAAa,GAAG,SAAAA,CAAUkB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;UACpCnB,OAAO,CAACoB,CAAC,GAAGtB,aAAa,CAACuB,IAAK;UAC/BrB,OAAO,CAACsB,CAAC,GAAGxB,aAAa,CAACyB,IAAK;UAC/B,IAAI;YACF,OAAON,EAAE,CAACC,EAAE,EAAEC,EAAG,CAAC;UACpB,CAAC,SAAS;YACRrB,aAAa,CAACyB,IAAI,GAAGvB,OAAO,CAACsB,CAAC;UAChC;QACF,CAAC;MACH;MACA,IAAIxB,aAAa,CAACY,UAAU,EAAE;QAC5BZ,aAAa,CAACA,aAAa,CAACY,UAAU,CAAC,GAAGV,OAAO,CAACI,CAAC;QACnDN,aAAa,CAACY,UAAU,GAAGc,SAAS;MACtC;MACA1B,aAAa,CAAC2B,IAAI,GAAGzB,OAAO,CAACI,CAAC;MAC9BN,aAAa,CAACyB,IAAI,GAAGvB,OAAO,CAACsB,CAAC;MAC9B,IAAI;QACF,OAAOzB,OAAO,CAAC6B,IAAI,CAAC,IAAI,EAAE5B,aAAa,CAAC;MAC1C,CAAC,SAAS;QACRE,OAAO,CAACoB,CAAC,GAAGtB,aAAa,CAACuB,IAAK;QAC/BrB,OAAO,CAACsB,CAAC,GAAGxB,aAAa,CAACyB,IAAI;MAChC;IACF,CAAC;EACH;EAGA,OAAO,CAAAI,OAAA,CAAAC,OAAA,GAACrD,mBAAmB,GAAG,SAAAA,CAAA,EAAY;IACxC,OAAO;MACLsD,IAAI,EAAE,SAAAA,CACJhC,OAAsB,EACtBiC,OAAkC,EAClCC,IAA+B,EAC/BC,WAAsC,EACtC;QACA,OAAOxD,CAAC,CAACyD,CAAC,CACRrC,WAAW,CAACC,OAAO,CAAC,EACpBiC,OAAO,EACPC,IAAI,EACJC,WAAW,IAAIA,WAAW,CAACE,OAAO,CAAC,CACrC,CAAC;MACH,CAAC;MACDhD,mBAAmB,EAAEA,mBAAmB;MACxCiD,IAAI,EAAE3D,CAAC,CAACG,CAAC;MACTyD,KAAK,EAAE,SAAAA,CAAUC,KAAU,EAAEC,IAAS,EAAE;QACtC,OAAO,IAAIC,sBAAa,CAACF,KAAK,EAAEC,IAAI,CAAC;MACvC,CAAC;MACDE,aAAa,EAAEA,iCAAa;MAC5BC,KAAK,EAAE,SAAAA,CACL5C,OAAsB,EACtBiC,OAAiB,EACjBC,IAAS,EACTC,WAAkB,EAClBU,WAAgC,EAChC;QACA,OAAO,CAACxD,mBAAmB,CAAC4C,OAAO,CAAC,GAAGa,4BAAQ,GAAGF,yBAAK,EACrD7C,WAAW,CAACC,OAAO,CAAC,EACpBiC,OAAO,EACPC,IAAI,EACJC,WAAW,EACXU,WACF,CAAC;MACH,CAAC;MACDE,IAAI,EAAEA,wBAAI;MACV/B,MAAM,EAAEA;IACV,CAAC;EACH,CAAC,EAAE,CAAC;AACN", "ignoreList": []}