'use strict';

const process = require('node:process');
const runner = require('./shared/ni.jp8Y2gho.cjs');
const fzf_es = require('./shared/ni.DWErt4DW.cjs');
const fs$1 = require('./shared/ni.DWRIF_6-.cjs');
const fs = require('node:fs');
const path = require('node:path');
require('readline');
require('events');
require('node:module');
require('child_process');
require('path');
require('process');
require('stream');
require('os');
require('tty');
require('node:os');
require('fs');
require('fs/promises');
require('node:fs/promises');

function _interopDefaultCompat (e) { return e && typeof e === 'object' && 'default' in e ? e.default : e; }

const process__default = /*#__PURE__*/_interopDefaultCompat(process);

const rawCompletionScript = `
###-begin-nr-completion-###

if type complete &>/dev/null; then
  _nr_completion() {
    local words
    local cur
    local cword
    _get_comp_words_by_ref -n =: cur words cword
    IFS=$'\\n'
    COMPREPLY=($(COMP_CWORD=$cword COMP_LINE=$cur nr --completion \${words[@]}))
  }
  complete -F _nr_completion nr
fi

###-end-nr-completion-###
`.trim();

let storage;
const storagePath = path.resolve(runner.CLI_TEMP_DIR, "_storage.json");
async function load(fn) {
  if (!storage) {
    storage = fs.existsSync(storagePath) ? JSON.parse(await fs.promises.readFile(storagePath, "utf-8") || "{}") || {} : {};
  }
  return storage;
}
async function dump() {
  if (storage)
    await runner.writeFileSafe(storagePath, JSON.stringify(storage));
}

function readPackageScripts(ctx) {
  const pkg = fs$1.getPackageJSON(ctx);
  const scripts = pkg.scripts || {};
  const scriptsInfo = pkg["scripts-info"] || {};
  return Object.entries(scripts).filter((i) => !i[0].startsWith("?")).map(([key, cmd]) => ({
    key,
    cmd,
    description: scriptsInfo[key] || scripts[`?${key}`] || cmd
  }));
}
runner.runCli(async (agent, args, ctx) => {
  const storage = await load();
  if (args[0] === "--completion") {
    const compLine = process__default.env.COMP_LINE;
    const rawCompCword = process__default.env.COMP_CWORD;
    if (compLine !== undefined && rawCompCword !== undefined) {
      const compCword = Number.parseInt(rawCompCword, 10);
      const compWords = args.slice(1);
      if (compCword === 1) {
        const raw = readPackageScripts(ctx);
        const fzf = new fzf_es.Fzf(raw, {
          selector: (item) => item.key,
          casing: "case-insensitive",
          tiebreakers: [fzf_es.byLengthAsc]
        });
        const results = fzf.find(compWords[1] || "");
        console.log(results.map((r) => r.item.key).join("\n"));
      }
    } else {
      console.log(rawCompletionScript);
    }
    return;
  }
  if (args[0] === "-") {
    if (!storage.lastRunCommand) {
      if (!ctx?.programmatic) {
        console.error("No last command found");
        process__default.exit(1);
      }
      throw new Error("No last command found");
    }
    args[0] = storage.lastRunCommand;
  }
  if (args.length === 0 && !ctx?.programmatic) {
    const raw = readPackageScripts(ctx);
    const terminalColumns = process__default.stdout?.columns || 80;
    const choices = raw.map(({ key, description }) => ({
      title: key,
      value: key,
      description: runner.limitText(description, terminalColumns - 15)
    }));
    const fzf = new fzf_es.Fzf(raw, {
      selector: (item) => `${item.key} ${item.description}`,
      casing: "case-insensitive",
      tiebreakers: [fzf_es.byLengthAsc]
    });
    if (storage.lastRunCommand) {
      const last = choices.find((i) => i.value === storage.lastRunCommand);
      if (last)
        choices.unshift(last);
    }
    try {
      const { fn } = await runner.prompts({
        name: "fn",
        message: "script to run",
        type: "autocomplete",
        choices,
        async suggest(input, choices2) {
          if (!input)
            return choices2;
          const results = fzf.find(input);
          return results.map((r) => choices2.find((c) => c.value === r.item.key));
        }
      });
      if (!fn)
        return;
      args.push(fn);
    } catch {
      process__default.exit(1);
    }
  }
  if (storage.lastRunCommand !== args[0]) {
    storage.lastRunCommand = args[0];
    dump();
  }
  return runner.parseNr(agent, args);
});
