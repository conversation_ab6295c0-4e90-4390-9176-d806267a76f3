'use strict';

const runner = require('./shared/ni.jp8Y2gho.cjs');
require('node:path');
require('node:process');
require('readline');
require('events');
require('node:module');
require('child_process');
require('path');
require('process');
require('stream');
require('node:fs');
require('os');
require('tty');
require('node:os');
require('fs');
require('fs/promises');
require('node:fs/promises');



exports.AGENTS = runner.AGENTS;
exports.CLI_TEMP_DIR = runner.CLI_TEMP_DIR;
exports.COMMANDS = runner.COMMANDS;
exports.INSTALL_PAGE = runner.INSTALL_PAGE;
exports.LOCKS = runner.LOCKS;
exports.UnsupportedCommand = runner.UnsupportedCommand;
exports.cmdExists = runner.cmdExists;
exports.constructCommand = runner.constructCommand;
exports.detect = runner.detect;
exports.exclude = runner.exclude;
exports.formatPackageWithUrl = runner.formatPackageWithUrl;
exports.getCliCommand = runner.getCliCommand;
exports.getCommand = runner.getCommand;
exports.getConfig = runner.getConfig;
exports.getDefaultAgent = runner.getDefaultAgent;
exports.getGlobalAgent = runner.getGlobalAgent;
exports.limitText = runner.limitText;
exports.parseNa = runner.parseNa;
exports.parseNi = runner.parseNi;
exports.parseNlx = runner.parseNlx;
exports.parseNr = runner.parseNr;
exports.parseNu = runner.parseNu;
exports.parseNun = runner.parseNun;
exports.remove = runner.remove;
exports.resolveCommand = runner.resolveCommand;
exports.run = runner.run;
exports.runCli = runner.runCli;
exports.serializeCommand = runner.serializeCommand;
exports.writeFileSafe = runner.writeFileSafe;
