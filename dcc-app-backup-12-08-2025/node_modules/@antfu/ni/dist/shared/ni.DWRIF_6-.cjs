'use strict';

const fs = require('node:fs');
const path = require('node:path');
const process = require('node:process');

function _interopDefaultCompat (e) { return e && typeof e === 'object' && 'default' in e ? e.default : e; }

const fs__default = /*#__PURE__*/_interopDefaultCompat(fs);
const process__default = /*#__PURE__*/_interopDefaultCompat(process);

function getPackageJSON(ctx) {
  const cwd = ctx?.cwd ?? process__default.cwd();
  const path$1 = path.resolve(cwd, "package.json");
  if (fs__default.existsSync(path$1)) {
    try {
      const raw = fs__default.readFileSync(path$1, "utf-8");
      const data = JSON.parse(raw);
      return data;
    } catch (e) {
      if (!ctx?.programmatic) {
        console.warn("Failed to parse package.json");
        process__default.exit(1);
      }
      throw e;
    }
  }
}

exports.getPackageJSON = getPackageJSON;
