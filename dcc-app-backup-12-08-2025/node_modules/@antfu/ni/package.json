{"name": "@antfu/ni", "type": "module", "version": "23.3.1", "packageManager": "pnpm@9.15.4", "description": "Use the right package manager", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/antfu-collective/ni#readme", "repository": {"type": "git", "url": "git+https://github.com/antfu-collective/ni.git"}, "bugs": {"url": "https://github.com/antfu-collective/ni/issues"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "bin": {"ni": "bin/ni.mjs", "nci": "bin/nci.mjs", "nr": "bin/nr.mjs", "nu": "bin/nu.mjs", "nlx": "bin/nlx.mjs", "na": "bin/na.mjs", "nun": "bin/nun.mjs"}, "files": ["bin", "dist"], "scripts": {"prepublishOnly": "npm run build", "dev": "tsx src/commands/ni.ts", "nr": "tsx src/commands/nr.ts", "build": "unbuild", "stub": "unbuild --stub", "release": "bumpp && npm publish", "typecheck": "tsc", "prepare": "npx simple-git-hooks", "lint": "eslint .", "test": "vitest"}, "devDependencies": {"@antfu/eslint-config": "^4.0.1", "@posva/prompts": "^2.4.4", "@types/fs-extra": "^11.0.4", "@types/ini": "^4.1.1", "@types/node": "^22.10.10", "@types/which": "^3.0.4", "bumpp": "^10.0.1", "eslint": "^9.19.0", "fs-extra": "^11.3.0", "fzf": "^0.5.2", "ini": "^5.0.0", "lint-staged": "^15.4.2", "package-manager-detector": "^0.2.9", "picocolors": "^1.1.1", "simple-git-hooks": "^2.11.1", "taze": "^18.3.0", "terminal-link": "^3.0.0", "tinyexec": "^0.3.2", "tinyglobby": "^0.2.10", "tsx": "^4.19.2", "typescript": "^5.7.3", "unbuild": "^3.3.1", "vitest": "^3.0.4", "which": "^5.0.0"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*": "eslint --fix"}}