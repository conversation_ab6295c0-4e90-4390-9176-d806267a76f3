"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/admin/skills-mapping",{

/***/ "(pages-dir-browser)/./core/components/ui/nav-admin.jsx":
/*!******************************************!*\
  !*** ./core/components/ui/nav-admin.jsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NavAdmin: () => (/* binding */ NavAdmin)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronRight_Combine_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,Combine!=!lucide-react */ \"(pages-dir-browser)/__barrel_optimize__?names=ChevronRight,Combine!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _core_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @core/components/ui/collapsible */ \"(pages-dir-browser)/./core/components/ui/collapsible.jsx\");\n/* harmony import */ var _core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @core/components/ui/sidebar */ \"(pages-dir-browser)/./core/components/ui/sidebar.jsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(pages-dir-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ NavAdmin auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction NavAdmin(param) {\n    let { userData, allMappings, deleteMappingRecord, typeSelected } = param;\n    _s();\n    const [skills, setskills] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(allMappings);\n    const removeItem = (id)=>{\n        const updatedSkills = [\n            ...skills\n        ];\n        const index = updatedSkills.findIndex((skill)=>skill.id === id);\n        if (index !== -1) {\n            updatedSkills.splice(index, 1);\n        }\n        setskills(updatedSkills);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarGroup, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarGroupLabel, {\n                className: \"min-w-8 bg-secondary text-primary-foreground\",\n                children: \"Admin tools\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenu, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_2__.Collapsible, {\n                    asChild: true,\n                    defaultOpen: false,\n                    className: \"group/collapsible\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenuItem, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_2__.CollapsibleTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenuButton, {\n                                    tooltip: \"Admin tools\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_Combine_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Combine, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                            href: \"/admin/skills-mapping\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Skills mapping\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                                    lineNumber: 57,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" \"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_Combine_lucide_react__WEBPACK_IMPORTED_MODULE_5__.ChevronRight, {\n                                            className: \"ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_2__.CollapsibleContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenuSub, {\n                                        children: \"Behavioural\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenuSub, {\n                                        children: \"test\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 16\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this),\n                            typeSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-sm font-extrabold tracking-tight text-balance text-dccblue pb-2\",\n                                            children: [\n                                                \"Skills mapped:\",\n                                                \" \",\n                                                allMappings && allMappings.length + \" / \" + \" 12\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: skills && skills.map((skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                id: skill.id,\n                                                class: \" mb-1 relative rounded-md flex bg-dccdarkgrey py-0.5 pl-2.5 pr-8 border border-transparent text-sm text-white transition-all shadow-sm font-semibold\",\n                                                children: [\n                                                    skill.skill_name,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"flex items-center justify-center transition-all p-1 rounded-md text-white hover:bg-white/10 active:bg-white/10 absolute top-0.5 right-0.5\",\n                                                        type: \"button\",\n                                                        onClick: ()=>{\n                                                            deleteMappingRecord(skill.id);\n                                                            removeItem(skill.id);\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            viewBox: \"0 0 16 16\",\n                                                            fill: \"currentColor\",\n                                                            class: \"w-4 h-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M5.28 4.22a.75.75 0 0 0-1.06 1.06L6.94 8l-2.72 2.72a.75.75 0 1 0 1.06 1.06L8 9.06l2.72 2.72a.75.75 0 1 0 1.06-1.06L9.06 8l2.72-2.72a.75.75 0 0 0-1.06-1.06L8 6.94 5.28 4.22Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                                                lineNumber: 99,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                                            lineNumber: 93,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                                        lineNumber: 85,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                lineNumber: 70,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, this)\n                }, 1, false, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n_s(NavAdmin, \"Dnbtuq+ayHWYdqRvHVFL/OXsZ6Y=\");\n_c = NavAdmin;\nvar _c;\n$RefreshReg$(_c, \"NavAdmin\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL2NvcmUvY29tcG9uZW50cy91aS9uYXYtYWRtaW4uanN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUU0QztBQUNTO0FBTVo7QUFVSjtBQUNSO0FBRXRCLFNBQVNnQixTQUFTLEtBS3hCO1FBTHdCLEVBQ3ZCQyxRQUFRLEVBQ1JDLFdBQVcsRUFDWEMsbUJBQW1CLEVBQ25CQyxZQUFZLEVBQ2IsR0FMd0I7O0lBTXZCLE1BQU0sQ0FBQ0MsUUFBUUMsVUFBVSxHQUFHdEIsK0NBQVFBLENBQUNrQjtJQUVyQyxNQUFNSyxhQUFhLENBQUNDO1FBQ2xCLE1BQU1DLGdCQUFnQjtlQUFJSjtTQUFPO1FBQ2pDLE1BQU1LLFFBQVFELGNBQWNFLFNBQVMsQ0FBQyxDQUFDQyxRQUFVQSxNQUFNSixFQUFFLEtBQUtBO1FBQzlELElBQUlFLFVBQVUsQ0FBQyxHQUFHO1lBQ2hCRCxjQUFjSSxNQUFNLENBQUNILE9BQU87UUFDOUI7UUFDQUosVUFBVUc7SUFDWjtJQUVBLHFCQUNFLDhEQUFDbEIscUVBQVlBOzswQkFDWCw4REFBQ0MsMEVBQWlCQTtnQkFBQ3NCLFdBQVU7MEJBQStDOzs7Ozs7MEJBRzVFLDhEQUFDckIsb0VBQVdBOzBCQUNWLDRFQUFDTCx3RUFBV0E7b0JBRVYyQixPQUFPO29CQUNQQyxhQUFhO29CQUNiRixXQUFVOzhCQUVWLDRFQUFDbkIsd0VBQWVBOzswQ0FDZCw4REFBQ0wsK0VBQWtCQTtnQ0FBQ3lCLE9BQU87MENBQ3pCLDRFQUFDckIsMEVBQWlCQTtvQ0FBQ3VCLFNBQVM7O3NEQUMxQiw4REFBQzlCLDZGQUFPQTs7Ozs7c0RBQ1IsOERBQUNZLGtEQUFJQTs0Q0FBQ21CLE1BQUs7OzhEQUNULDhEQUFDQzs4REFBSzs7Ozs7O2dEQUFzQjs7Ozs7OztzREFFOUIsOERBQUNqQyxrR0FBWUE7NENBQUM0QixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OzswQ0FHNUIsOERBQUN6QiwrRUFBa0JBOztrREFDakIsOERBQUNPLHVFQUFjQTtrREFBQzs7Ozs7O2tEQUNmLDhEQUFDQSx1RUFBY0E7a0RBQUM7Ozs7Ozs7Ozs7Ozs0QkFLbEJRLDhCQUNDLDhEQUFDZ0I7Z0NBQUlOLFdBQVU7O2tEQUNiLDhEQUFDTTtrREFDQyw0RUFBQ0M7NENBQUdQLFdBQVU7O2dEQUF1RTtnREFDcEU7Z0RBQ2RaLGVBQWVBLFlBQVlvQixNQUFNLEdBQUcsUUFBUTs7Ozs7Ozs7Ozs7O2tEQUdqRCw4REFBQ0Y7d0NBQUlOLFdBQVU7a0RBQ1pULFVBQ0NBLE9BQU9rQixHQUFHLENBQUMsQ0FBQ1gsc0JBQ1YsOERBQUNRO2dEQUNDWixJQUFJSSxNQUFNSixFQUFFO2dEQUNaZ0IsT0FBTTs7b0RBRUxaLE1BQU1hLFVBQVU7a0VBQ2pCLDhEQUFDQzt3REFDQ1osV0FBVTt3REFDVmEsTUFBSzt3REFDTEMsU0FBUzs0REFDUHpCLG9CQUFvQlMsTUFBTUosRUFBRTs0REFDNUJELFdBQVdLLE1BQU1KLEVBQUU7d0RBQ3JCO2tFQUVBLDRFQUFDcUI7NERBQ0NDLE9BQU07NERBQ05DLFNBQVE7NERBQ1JDLE1BQUs7NERBQ0xSLE9BQU07c0VBRU4sNEVBQUNTO2dFQUFLQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7bUJBcERyQjs7Ozs7Ozs7Ozs7Ozs7OztBQWlFZjtHQXpGZ0JsQztLQUFBQSIsInNvdXJjZXMiOlsiL1VzZXJzL3JpamFzdC9fX2RhdGEvX19kZXYvRXF1YWxpdGFsL2Rldi9kY2MvZGNjLWFwcC9kY2MtYXBwL2NvcmUvY29tcG9uZW50cy91aS9uYXYtYWRtaW4uanN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBDaGV2cm9uUmlnaHQsIENvbWJpbmUgfSBmcm9tIFwibHVjaWRlLXJlYWN0XCI7XG5cbmltcG9ydCB7XG4gIENvbGxhcHNpYmxlLFxuICBDb2xsYXBzaWJsZUNvbnRlbnQsXG4gIENvbGxhcHNpYmxlVHJpZ2dlcixcbn0gZnJvbSBcIkBjb3JlL2NvbXBvbmVudHMvdWkvY29sbGFwc2libGVcIjtcbmltcG9ydCB7XG4gIFNpZGViYXJHcm91cCxcbiAgU2lkZWJhckdyb3VwTGFiZWwsXG4gIFNpZGViYXJNZW51LFxuICBTaWRlYmFyTWVudUJ1dHRvbixcbiAgU2lkZWJhck1lbnVJdGVtLFxuICBTaWRlYmFyTWVudVN1YixcbiAgU2lkZWJhck1lbnVTdWJCdXR0b24sXG4gIFNpZGViYXJNZW51U3ViSXRlbSxcbn0gZnJvbSBcIkBjb3JlL2NvbXBvbmVudHMvdWkvc2lkZWJhclwiO1xuaW1wb3J0IExpbmsgZnJvbSBcIm5leHQvbGlua1wiO1xuXG5leHBvcnQgZnVuY3Rpb24gTmF2QWRtaW4oe1xuICB1c2VyRGF0YSxcbiAgYWxsTWFwcGluZ3MsXG4gIGRlbGV0ZU1hcHBpbmdSZWNvcmQsXG4gIHR5cGVTZWxlY3RlZCxcbn0pIHtcbiAgY29uc3QgW3NraWxscywgc2V0c2tpbGxzXSA9IHVzZVN0YXRlKGFsbE1hcHBpbmdzKTtcblxuICBjb25zdCByZW1vdmVJdGVtID0gKGlkKSA9PiB7XG4gICAgY29uc3QgdXBkYXRlZFNraWxscyA9IFsuLi5za2lsbHNdO1xuICAgIGNvbnN0IGluZGV4ID0gdXBkYXRlZFNraWxscy5maW5kSW5kZXgoKHNraWxsKSA9PiBza2lsbC5pZCA9PT0gaWQpO1xuICAgIGlmIChpbmRleCAhPT0gLTEpIHtcbiAgICAgIHVwZGF0ZWRTa2lsbHMuc3BsaWNlKGluZGV4LCAxKTtcbiAgICB9XG4gICAgc2V0c2tpbGxzKHVwZGF0ZWRTa2lsbHMpO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPFNpZGViYXJHcm91cD5cbiAgICAgIDxTaWRlYmFyR3JvdXBMYWJlbCBjbGFzc05hbWU9XCJtaW4tdy04IGJnLXNlY29uZGFyeSB0ZXh0LXByaW1hcnktZm9yZWdyb3VuZFwiPlxuICAgICAgICBBZG1pbiB0b29sc1xuICAgICAgPC9TaWRlYmFyR3JvdXBMYWJlbD5cbiAgICAgIDxTaWRlYmFyTWVudT5cbiAgICAgICAgPENvbGxhcHNpYmxlXG4gICAgICAgICAga2V5PXsxfVxuICAgICAgICAgIGFzQ2hpbGRcbiAgICAgICAgICBkZWZhdWx0T3Blbj17ZmFsc2V9XG4gICAgICAgICAgY2xhc3NOYW1lPVwiZ3JvdXAvY29sbGFwc2libGVcIlxuICAgICAgICA+XG4gICAgICAgICAgPFNpZGViYXJNZW51SXRlbT5cbiAgICAgICAgICAgIDxDb2xsYXBzaWJsZVRyaWdnZXIgYXNDaGlsZD5cbiAgICAgICAgICAgICAgPFNpZGViYXJNZW51QnV0dG9uIHRvb2x0aXA9e1wiQWRtaW4gdG9vbHNcIn0+XG4gICAgICAgICAgICAgICAgPENvbWJpbmUgLz5cbiAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2FkbWluL3NraWxscy1tYXBwaW5nXCI+XG4gICAgICAgICAgICAgICAgICA8c3Bhbj5Ta2lsbHMgbWFwcGluZzwvc3Bhbj57XCIgXCJ9XG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgIDxDaGV2cm9uUmlnaHQgY2xhc3NOYW1lPVwibWwtYXV0byB0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi0yMDAgZ3JvdXAtZGF0YS1bc3RhdGU9b3Blbl0vY29sbGFwc2libGU6cm90YXRlLTkwXCIgLz5cbiAgICAgICAgICAgICAgPC9TaWRlYmFyTWVudUJ1dHRvbj5cbiAgICAgICAgICAgIDwvQ29sbGFwc2libGVUcmlnZ2VyPlxuICAgICAgICAgICAgPENvbGxhcHNpYmxlQ29udGVudD5cbiAgICAgICAgICAgICAgPFNpZGViYXJNZW51U3ViPkJlaGF2aW91cmFsPC9TaWRlYmFyTWVudVN1Yj5cbiAgICAgICAgICAgICAgIDxTaWRlYmFyTWVudVN1Yj50ZXN0PC9TaWRlYmFyTWVudVN1Yj5cbiAgICAgICAgICAgIDwvQ29sbGFwc2libGVDb250ZW50PlxuXG4gICAgICAgICAgICB7LyogPENoZXZyb25SaWdodCBjbGFzc05hbWU9XCJtbC1hdXRvIHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTIwMCBncm91cC1kYXRhLVtzdGF0ZT1vcGVuXS9jb2xsYXBzaWJsZTpyb3RhdGUtOTBcIiAvPiAqL31cblxuICAgICAgICAgICAge3R5cGVTZWxlY3RlZCAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0zXCI+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtZXh0cmFib2xkIHRyYWNraW5nLXRpZ2h0IHRleHQtYmFsYW5jZSB0ZXh0LWRjY2JsdWUgcGItMlwiPlxuICAgICAgICAgICAgICAgICAgICBTa2lsbHMgbWFwcGVkOntcIiBcIn1cbiAgICAgICAgICAgICAgICAgICAge2FsbE1hcHBpbmdzICYmIGFsbE1hcHBpbmdzLmxlbmd0aCArIFwiIC8gXCIgKyBcIiAxMlwifVxuICAgICAgICAgICAgICAgICAgPC9oMT5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICB7c2tpbGxzICYmXG4gICAgICAgICAgICAgICAgICAgIHNraWxscy5tYXAoKHNraWxsKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICAgICAgaWQ9e3NraWxsLmlkfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3M9XCIgbWItMSByZWxhdGl2ZSByb3VuZGVkLW1kIGZsZXggYmctZGNjZGFya2dyZXkgcHktMC41IHBsLTIuNSBwci04IGJvcmRlciBib3JkZXItdHJhbnNwYXJlbnQgdGV4dC1zbSB0ZXh0LXdoaXRlIHRyYW5zaXRpb24tYWxsIHNoYWRvdy1zbSBmb250LXNlbWlib2xkXCJcbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICB7c2tpbGwuc2tpbGxfbmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgdHJhbnNpdGlvbi1hbGwgcC0xIHJvdW5kZWQtbWQgdGV4dC13aGl0ZSBob3ZlcjpiZy13aGl0ZS8xMCBhY3RpdmU6Ymctd2hpdGUvMTAgYWJzb2x1dGUgdG9wLTAuNSByaWdodC0wLjVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRlbGV0ZU1hcHBpbmdSZWNvcmQoc2tpbGwuaWQpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlbW92ZUl0ZW0oc2tpbGwuaWQpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3ZnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmlld0JveD1cIjAgMCAxNiAxNlwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZmlsbD1cImN1cnJlbnRDb2xvclwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3M9XCJ3LTQgaC00XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9XCJNNS4yOCA0LjIyYS43NS43NSAwIDAgMC0xLjA2IDEuMDZMNi45NCA4bC0yLjcyIDIuNzJhLjc1Ljc1IDAgMSAwIDEuMDYgMS4wNkw4IDkuMDZsMi43MiAyLjcyYS43NS43NSAwIDEgMCAxLjA2LTEuMDZMOS4wNiA4bDIuNzItMi43MmEuNzUuNzUgMCAwIDAtMS4wNi0xLjA2TDggNi45NCA1LjI4IDQuMjJaXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L1NpZGViYXJNZW51SXRlbT5cbiAgICAgICAgPC9Db2xsYXBzaWJsZT5cbiAgICAgIDwvU2lkZWJhck1lbnU+XG4gICAgPC9TaWRlYmFyR3JvdXA+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJDaGV2cm9uUmlnaHQiLCJDb21iaW5lIiwiQ29sbGFwc2libGUiLCJDb2xsYXBzaWJsZUNvbnRlbnQiLCJDb2xsYXBzaWJsZVRyaWdnZXIiLCJTaWRlYmFyR3JvdXAiLCJTaWRlYmFyR3JvdXBMYWJlbCIsIlNpZGViYXJNZW51IiwiU2lkZWJhck1lbnVCdXR0b24iLCJTaWRlYmFyTWVudUl0ZW0iLCJTaWRlYmFyTWVudVN1YiIsIlNpZGViYXJNZW51U3ViQnV0dG9uIiwiU2lkZWJhck1lbnVTdWJJdGVtIiwiTGluayIsIk5hdkFkbWluIiwidXNlckRhdGEiLCJhbGxNYXBwaW5ncyIsImRlbGV0ZU1hcHBpbmdSZWNvcmQiLCJ0eXBlU2VsZWN0ZWQiLCJza2lsbHMiLCJzZXRza2lsbHMiLCJyZW1vdmVJdGVtIiwiaWQiLCJ1cGRhdGVkU2tpbGxzIiwiaW5kZXgiLCJmaW5kSW5kZXgiLCJza2lsbCIsInNwbGljZSIsImNsYXNzTmFtZSIsImFzQ2hpbGQiLCJkZWZhdWx0T3BlbiIsInRvb2x0aXAiLCJocmVmIiwic3BhbiIsImRpdiIsImgxIiwibGVuZ3RoIiwibWFwIiwiY2xhc3MiLCJza2lsbF9uYW1lIiwiYnV0dG9uIiwidHlwZSIsIm9uQ2xpY2siLCJzdmciLCJ4bWxucyIsInZpZXdCb3giLCJmaWxsIiwicGF0aCIsImQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./core/components/ui/nav-admin.jsx\n"));

/***/ })

});