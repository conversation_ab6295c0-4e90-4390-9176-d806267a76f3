"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/admin/skills-mapping",{

/***/ "(pages-dir-browser)/./pages/admin/skills-mapping.js":
/*!***************************************!*\
  !*** ./pages/admin/skills-mapping.js ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSG: () => (/* binding */ __N_SSG),\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(pages-dir-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @utils/supabaseClient */ \"(pages-dir-browser)/./utils/supabaseClient.js\");\n/* harmony import */ var svg_loaders_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! svg-loaders-react */ \"(pages-dir-browser)/./node_modules/svg-loaders-react/dist/index.js\");\n/* harmony import */ var svg_loaders_react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(svg_loaders_react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @core/components/ui/sidebar */ \"(pages-dir-browser)/./core/components/ui/sidebar.jsx\");\n/* harmony import */ var _core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @core/components/ui/breadcrumb */ \"(pages-dir-browser)/./core/components/ui/breadcrumb.jsx\");\n/* harmony import */ var _core_components_app_sidebar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @core/components/app-sidebar */ \"(pages-dir-browser)/./core/components/app-sidebar.jsx\");\n/* harmony import */ var _core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @core/components/ui/select */ \"(pages-dir-browser)/./core/components/ui/select.jsx\");\n/* harmony import */ var _core_components_ui_label__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @core/components/ui/label */ \"(pages-dir-browser)/./core/components/ui/label.jsx\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,ChevronsUpDown,CircleCheck!=!lucide-react */ \"(pages-dir-browser)/__barrel_optimize__?names=Check,ChevronLeft,ChevronRight,ChevronsUpDown,CircleCheck!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _core_lib_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @core/lib/utils */ \"(pages-dir-browser)/./core/lib/utils.js\");\n/* harmony import */ var _core_components_ui_button__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @core/components/ui/button */ \"(pages-dir-browser)/./core/components/ui/button.jsx\");\n/* harmony import */ var _core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @core/components/ui/command */ \"(pages-dir-browser)/./core/components/ui/command.jsx\");\n/* harmony import */ var _core_components_ui_popover__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @core/components/ui/popover */ \"(pages-dir-browser)/./core/components/ui/popover.jsx\");\n/* harmony import */ var core_TSkillsLib__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! core/TSkillsLib */ \"(pages-dir-browser)/./core/TSkillsLib.js\");\n/* harmony import */ var core_BSkillsLib__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! core/BSkillsLib */ \"(pages-dir-browser)/./core/BSkillsLib.js\");\n/* harmony import */ var core_Mapper_MapperSkillsLibHeader__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! core/Mapper/MapperSkillsLibHeader */ \"(pages-dir-browser)/./core/Mapper/MapperSkillsLibHeader.js\");\n/* harmony import */ var core_Mapper_MapperSkillRow__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! core/Mapper/MapperSkillRow */ \"(pages-dir-browser)/./core/Mapper/MapperSkillRow.js\");\n/* harmony import */ var core_MapperB_MapperBSkillsLibHeader__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! core/MapperB/MapperBSkillsLibHeader */ \"(pages-dir-browser)/./core/MapperB/MapperBSkillsLibHeader.js\");\n/* harmony import */ var core_MapperB_MapperBSkillRow__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! core/MapperB/MapperBSkillRow */ \"(pages-dir-browser)/./core/MapperB/MapperBSkillRow.js\");\n/* harmony import */ var _core_Toaster__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @core/Toaster */ \"(pages-dir-browser)/./core/Toaster.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar __N_SSG = true;\nfunction Dashboard(param) {\n    let { behavioural_skills, technical_skills } = param;\n    var _behavioural_skills_find, _technical_skills_find;\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userRole, setUserRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userRoles, setUserRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [typeSelected, setTypeSelected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [roleSelected, setRoleSelected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [skillSelected, setSkillSelected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [skillSelectedId, setSkillSelectedId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [value, setValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedLevel, setSelectedLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [existingMap, setExistingMap] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [allMappings, setAllMappings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            // Check for existing session\n            _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getSession().then({\n                \"Dashboard.useEffect\": (param)=>{\n                    let { data: { session } } = param;\n                    setSession(session);\n                    if (!session) {\n                        // Redirect to index page if no session\n                        router.push(\"/\");\n                    }\n                }\n            }[\"Dashboard.useEffect\"]);\n            // Listen for auth state changes\n            const { data: { subscription } } = _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.onAuthStateChange({\n                \"Dashboard.useEffect\": (_event, session)=>{\n                    setSession(session);\n                    if (!session) {\n                        // Redirect to index page if session is lost\n                        router.push(\"/\");\n                    }\n                }\n            }[\"Dashboard.useEffect\"]);\n            return ({\n                \"Dashboard.useEffect\": ()=>subscription.unsubscribe()\n            })[\"Dashboard.useEffect\"];\n        }\n    }[\"Dashboard.useEffect\"], [\n        router\n    ]);\n    // Fetch user profile when session is available\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if (session) {\n                getUserProfile();\n            }\n        }\n    }[\"Dashboard.useEffect\"], [\n        session\n    ]);\n    // useEffect(() => {\n    //   if (skillSelectedId) {\n    //     getMappings();\n    //   }\n    // }, [skillSelected]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if (roleSelected) {\n                getAllMappings(roleSelected);\n            }\n        }\n    }[\"Dashboard.useEffect\"], [\n        roleSelected,\n        skillSelected\n    ]);\n    /* --- DEBUG --- */ // console.log(\"existingMap\");\n    // console.log(existingMap);\n    // console.log(\"typeSelected\");\n    // console.log(typeSelected);\n    // console.log(\"allMappings\");\n    // console.log(allMappings);\n    // console.log(\"roleSelected\");\n    // console.log(roleSelected);\n    // console.log(\"skillSelected\");\n    // console.log(skillSelected);\n    // console.log(\"skillSelectedId\");\n    // console.log(skillSelectedId && skillSelectedId.id);\n    // console.log(\"selectedLevel\");\n    // console.log(selectedLevel);\n    // console.log(\"userData\");\n    // console.log(userData);\n    // console.log(\"userRole\");\n    // console.log(userRole);\n    // console.log(\"userRoles\");\n    // console.log(userRoles);\n    // console.log(\"behavioural_skills\");\n    // console.log(behavioural_skills);\n    // console.log(\"technical_skills\");\n    // console.log(technical_skills);\n    /* --- DEBUG --- */ const [lvl1, setLvl1] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lvl2, setLvl2] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lvl3, setLvl3] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lvl4, setLvl4] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lvl5, setLvl5] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // console.log(isChecked ? \"checked\" : \"unchecked\");\n    const handleOnChange = (level)=>{\n        // Reset all levels if the current level is being unchecked\n        if (level === 1 && lvl1 || level === 2 && lvl2 || level === 3 && lvl3 || level === 4 && lvl4 || level === 5 && lvl5) {\n            setLvl1(false);\n            setLvl2(false);\n            setLvl3(false);\n            setLvl4(false);\n            setLvl5(false);\n            return;\n        }\n        // Set all levels to false first\n        setLvl1(false);\n        setLvl2(false);\n        setLvl3(false);\n        setLvl4(false);\n        setLvl5(false);\n        // Then set only the selected level to true\n        switch(level){\n            case 1:\n                setLvl1(true);\n                setSelectedLevel(1);\n                break;\n            case 2:\n                setLvl2(true);\n                setSelectedLevel(2);\n                break;\n            case 3:\n                setLvl3(true);\n                setSelectedLevel(3);\n                break;\n            case 4:\n                setLvl4(true);\n                setSelectedLevel(4);\n                break;\n            case 5:\n                setLvl5(true);\n                setSelectedLevel(5);\n                break;\n        }\n    };\n    // select role\n    const selectRole = (roleSelected)=>{\n        console.log(\"roleSelected\");\n        setRoleSelected(roleSelected);\n    // setTypeSelected(null);\n    // setSkillSelectedId(null);\n    };\n    const showSaveConfirm = ()=>{\n        sonner__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Mapping created\", {\n            classNames: {\n                icon: \"!text-green-600\"\n            }\n        });\n    };\n    const showError = ()=>{\n        sonner__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Error - not saved, make sure level is selected\", {\n            classNames: {\n                icon: \"!text-red-600\"\n            }\n        });\n    };\n    const getSelectedRecord = (skill_id)=>{\n        // if behavioural or technical, get the record from the array\n        if (typeSelected === \"Behavioural\") {\n            const selectedSkillsRec = behavioural_skills.find((record)=>record.id === skill_id);\n            return selectedSkillsRec;\n        }\n        if (typeSelected === \"Technical\") {\n            const selectedSkillsRec = technical_skills.find((record)=>record.id === skill_id);\n            return selectedSkillsRec;\n        }\n    };\n    async function getUserProfile() {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"profiles\").select(\"*\").eq(\"id\", user.id).single();\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                setUserData(data);\n                getUserRole(data.team_role);\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    // get user's role\n    async function getUserRole(id) {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"roles\").select(\"id, team\").eq(\"id\", id).single();\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                setUserRole(data);\n                getRoles(data.team);\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    // get roles based on user\n    async function getRoles(team) {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"roles\").select(\"id, team, role_name\").eq(\"team\", team);\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                setUserRoles(data);\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    // get existing mappings based on user's team\n    async function getMappings(type) {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"role_mapping\").select().eq(\"skill_id\", skillSelectedId && skillSelectedId.id) // add role here too!\n            .eq(\"role_id\", roleSelected).single();\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                setExistingMap(data);\n                handleOnChange(data.skill_level);\n            } else {\n                console.log(\"no existing data\");\n                handleOnChange(null);\n                setSelectedLevel(null);\n                setExistingMap(null);\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    // get all existing mappings for the role\n    async function getAllMappings(roleId) {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"role_mapping\").select().eq(\"role_id\", roleId);\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                setAllMappings(data);\n            } else {\n                console.log(\"no existing data\");\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    // update existing or create new mapping\n    const createMappingRecord = ()=>{\n        if (existingMap) {\n            updateMapping(existingMap && existingMap.id);\n        } else {\n            createMapping();\n        }\n    };\n    // create mapping for currently selected skill\n    async function createMapping() {\n        try {\n            // setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"role_mapping\").insert({\n                role_id: roleSelected,\n                skill_type: typeSelected,\n                skill_id: skillSelectedId && skillSelectedId.id,\n                skill_level: selectedLevel,\n                skill_name: skillSelectedId && skillSelectedId.skill_name\n            });\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (status == 201) {\n                console.log(status);\n                showSaveConfirm();\n            }\n        } catch (error) {\n            console.log(\"Error:\", error.message);\n            showError(error.message);\n        } finally{\n        // setLoading(false);\n        }\n    }\n    async function updateMapping(id) {\n        try {\n            // setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"role_mapping\").update({\n                skill_level: selectedLevel\n            }).eq(\"id\", id);\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (status == 204) {\n                console.log(status);\n                showSaveConfirm();\n            }\n        } catch (error) {\n            console.log(\"Error:\", error.message);\n            showError(error.message);\n        } finally{\n        // setLoading(false);\n        }\n    }\n    // delete a mapping record\n    const deleteMappingRecord = (id)=>{\n        deleteMapping(id);\n    };\n    async function deleteMapping(id) {\n        try {\n            // setLoading(true);\n            let { error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"role_mapping\").delete().eq(\"id\", id);\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (status == 204) {\n                console.log(status);\n            // showSaveConfirm();\n            }\n        } catch (error) {\n            console.log(\"Error:\", error.message);\n            showError(error.message);\n        } finally{\n        // setLoading(false);\n        }\n    }\n    // Show loading state while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid place-items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-10 mb-10 flex flex-row space-x-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(svg_loaders_react__WEBPACK_IMPORTED_MODULE_5__.Oval, {\n                        stroke: \"#0c39ac\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                        lineNumber: 568,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                    lineNumber: 567,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                lineNumber: 566,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n            lineNumber: 565,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render dashboard if no session (will redirect)\n    if (!session) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_app_sidebar__WEBPACK_IMPORTED_MODULE_8__.AppSidebar, {\n                userData: userData,\n                behavioural_skills: behavioural_skills,\n                technical_skills: technical_skills,\n                allMappings: allMappings,\n                deleteMappingRecord: deleteMappingRecord,\n                typeSelected: typeSelected\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                lineNumber: 582,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarInset, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"flex h-16 shrink-0 items-center gap-2 border-b\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 px-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarTrigger, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                    lineNumber: 594,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__.Breadcrumb, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__.BreadcrumbList, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__.BreadcrumbItem, {\n                                                className: \"hidden md:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__.BreadcrumbLink, {\n                                                    href: \"#\",\n                                                    children: \"Admin tools\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                    lineNumber: 599,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                lineNumber: 598,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__.BreadcrumbSeparator, {\n                                                className: \"hidden md:block\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                lineNumber: 601,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__.BreadcrumbItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__.BreadcrumbLink, {\n                                                    children: \"Skills mapping\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                    lineNumber: 603,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                lineNumber: 602,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                        lineNumber: 597,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                    lineNumber: 596,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                            lineNumber: 593,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                        lineNumber: 592,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-1 flex-row gap-2 p-2 pt-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex m-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-6 pt-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                onValueChange: (roleSelected)=>selectRole(roleSelected),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                        className: \"text-sm text-center pb-1 text-primary font-semibold\",\n                                                        htmlFor: \"skill type\",\n                                                        children: \"Role\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 623,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                        className: \"w-[300px]\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                            placeholder: \"Select a role\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 630,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 629,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                                        children: userRoles && userRoles.map((role)=>{\n                                                            if (role.role_name !== \"Team Owner\") {\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                    value: role.id,\n                                                                    children: role.role_name\n                                                                }, role.id, false, {\n                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                    lineNumber: 637,\n                                                                    columnNumber: 29\n                                                                }, this);\n                                                            }\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 632,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                lineNumber: 617,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                            lineNumber: 616,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-6 pt-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                // onValueChange={(skillSelected) =>\n                                                //   setTypeSelected(skillSelected)\n                                                // }\n                                                // pass the type to the get mappings query\n                                                onValueChange: (value)=>getMappings(value),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                        className: \"text-sm text-center pb-1  text-primary font-semibold\",\n                                                        htmlFor: \"skill type\",\n                                                        children: \"Skill type\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 655,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                        className: \"w-[150px]\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                            placeholder: \"Select type\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 662,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 661,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                value: \"behavioural\",\n                                                                children: \"Behavioural\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                lineNumber: 665,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                value: \"technical\",\n                                                                children: \"Technical\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                lineNumber: 666,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 664,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                lineNumber: 648,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                            lineNumber: 647,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-6 pt-0\",\n                                            children: [\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex ml-3 pt-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_button__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__.ChevronLeft, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 674,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 673,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                    lineNumber: 672,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                            lineNumber: 670,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3 pt-0\",\n                                            children: [\n                                                !typeSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                    disabled: true,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                            className: \"text-sm text-center pb-1 text-primary font-semibold\",\n                                                            htmlFor: \"skill type\",\n                                                            children: \"Skill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 681,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                            className: \"w-[220px]\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                                placeholder: \"Select a skill\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                lineNumber: 688,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 687,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                    lineNumber: 680,\n                                                    columnNumber: 19\n                                                }, this),\n                                                typeSelected === \"Behavioural\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_popover__WEBPACK_IMPORTED_MODULE_14__.Popover, {\n                                                    open: open,\n                                                    onOpenChange: setOpen,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                            className: \"text-sm text-center pb-1 text-primary font-semibold\",\n                                                            htmlFor: \"skill type\",\n                                                            children: \"Skill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 695,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_popover__WEBPACK_IMPORTED_MODULE_14__.PopoverTrigger, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_button__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                                                variant: \"outline\",\n                                                                role: \"combobox\",\n                                                                \"aria-expanded\": open,\n                                                                className: \"w-[300px] justify-between text-muted-foreground\",\n                                                                children: [\n                                                                    skillSelected ? (_behavioural_skills_find = behavioural_skills.find((skill)=>skill.skill_name === skillSelected)) === null || _behavioural_skills_find === void 0 ? void 0 : _behavioural_skills_find.skill_name : \"Select skill\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__.ChevronsUpDown, {\n                                                                        className: \"opacity-50\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                        lineNumber: 713,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                lineNumber: 702,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 701,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_popover__WEBPACK_IMPORTED_MODULE_14__.PopoverContent, {\n                                                            className: \"w-[200px] p-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.Command, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandInput, {\n                                                                        placeholder: \"Search skills...\",\n                                                                        className: \"h-9\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                        lineNumber: 718,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandList, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandEmpty, {\n                                                                                children: \"No match found\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                lineNumber: 723,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandGroup, {\n                                                                                children: behavioural_skills.map((skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandItem, {\n                                                                                        value: skill.skill_name,\n                                                                                        onSelect: (currentValue)=>{\n                                                                                            setSkillSelected(currentValue === skillSelected ? \"\" : currentValue);\n                                                                                            setSkillSelectedId(getSelectedRecord(skill.id));\n                                                                                            setOpen(false);\n                                                                                        },\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__.CircleCheck, {\n                                                                                                className: allMappings.find((mapping)=>mapping.skill_id === skill.id) ? \"text-yellow-500\" : \"opacity-0\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                                lineNumber: 741,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            skill.skill_name,\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__.Check, {\n                                                                                                className: (0,_core_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"ml-auto\", skillSelected === skill.skill_name ? \"opacity-100\" : \"opacity-0\")\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                                lineNumber: 751,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, skill.skill_name, true, {\n                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                        lineNumber: 726,\n                                                                                        columnNumber: 31\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                lineNumber: 724,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                        lineNumber: 722,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                lineNumber: 717,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 716,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                    lineNumber: 694,\n                                                    columnNumber: 19\n                                                }, this),\n                                                typeSelected === \"Technical\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_popover__WEBPACK_IMPORTED_MODULE_14__.Popover, {\n                                                    open: open,\n                                                    onOpenChange: setOpen,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                            className: \"text-sm text-center pb-1 text-primary font-semibold\",\n                                                            htmlFor: \"skill type\",\n                                                            children: \"Skill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 770,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_popover__WEBPACK_IMPORTED_MODULE_14__.PopoverTrigger, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_button__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                                                variant: \"outline\",\n                                                                role: \"combobox\",\n                                                                \"aria-expanded\": open,\n                                                                className: \"w-[300px] justify-between\",\n                                                                children: [\n                                                                    skillSelected ? (_technical_skills_find = technical_skills.find((skill)=>skill.skill_name === skillSelected)) === null || _technical_skills_find === void 0 ? void 0 : _technical_skills_find.skill_name : \"Select skill\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__.ChevronsUpDown, {\n                                                                        className: \"opacity-50\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                        lineNumber: 788,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                lineNumber: 777,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 776,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_popover__WEBPACK_IMPORTED_MODULE_14__.PopoverContent, {\n                                                            className: \"w-[200px] p-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.Command, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandInput, {\n                                                                        placeholder: \"Search skills...\",\n                                                                        className: \"h-9\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                        lineNumber: 793,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandList, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandEmpty, {\n                                                                                children: \"No match found\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                lineNumber: 798,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandGroup, {\n                                                                                children: technical_skills.map((skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandItem, {\n                                                                                        value: skill.skill_name,\n                                                                                        onSelect: (currentValue)=>{\n                                                                                            setSkillSelected(currentValue === skillSelected ? \"\" : currentValue);\n                                                                                            setSkillSelectedId(getSelectedRecord(skill.id));\n                                                                                            setOpen(false);\n                                                                                        },\n                                                                                        children: [\n                                                                                            skill.skill_name,\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__.Check, {\n                                                                                                className: (0,_core_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"ml-auto\", skillSelected === skill.skill_name ? \"opacity-100\" : \"opacity-0\")\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                                lineNumber: 819,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, skill.skill_name, true, {\n                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                        lineNumber: 801,\n                                                                                        columnNumber: 31\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                lineNumber: 799,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                        lineNumber: 797,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                lineNumber: 792,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 791,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                    lineNumber: 769,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                            lineNumber: 678,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-0 pt-0\",\n                                            children: [\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex ml-3=0 pt-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_button__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                                        className: \"ml-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__.ChevronRight, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 840,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 839,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                    lineNumber: 838,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                            lineNumber: 836,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex ml-3 pt-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_button__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                                className: \"bg-green-600 hover:bg-green-600/90\",\n                                                onClick: createMappingRecord,\n                                                children: \"Save selection\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                lineNumber: 846,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                            lineNumber: 845,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                    lineNumber: 615,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                lineNumber: 614,\n                                columnNumber: 11\n                            }, this),\n                            skillSelected && typeSelected === \"Behavioural\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(core_BSkillsLib__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(core_MapperB_MapperBSkillsLibHeader__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                        lineNumber: 858,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(core_MapperB_MapperBSkillRow__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        behavioural_sub_skills: skillSelectedId.behavioural_sub_skills,\n                                        handleOnChange: handleOnChange,\n                                        lvl1: lvl1,\n                                        lvl2: lvl2,\n                                        lvl3: lvl3,\n                                        lvl4: lvl4,\n                                        lvl5: lvl5\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                        lineNumber: 859,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                lineNumber: 857,\n                                columnNumber: 13\n                            }, this),\n                            skillSelected && typeSelected === \"Technical\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(core_TSkillsLib__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(core_Mapper_MapperSkillsLibHeader__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                        lineNumber: 873,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(core_Mapper_MapperSkillRow__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        technical_sub_skills: skillSelectedId.technical_sub_skills,\n                                        handleOnChange: handleOnChange,\n                                        lvl1: lvl1,\n                                        lvl2: lvl2,\n                                        lvl3: lvl3,\n                                        lvl4: lvl4\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                        lineNumber: 874,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                lineNumber: 872,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_Toaster__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                position: \"bottom-right\",\n                                toastOptions: {\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                lineNumber: 884,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                        lineNumber: 609,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                lineNumber: 591,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n        lineNumber: 581,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"gldjzi+fY/08eNFn7LFFxJAkRSI=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/admin/skills-mapping.js\n"));

/***/ })

});