"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/admin/skills-mapping",{

/***/ "(pages-dir-browser)/./pages/admin/skills-mapping.js":
/*!***************************************!*\
  !*** ./pages/admin/skills-mapping.js ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSG: () => (/* binding */ __N_SSG),\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(pages-dir-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @utils/supabaseClient */ \"(pages-dir-browser)/./utils/supabaseClient.js\");\n/* harmony import */ var svg_loaders_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! svg-loaders-react */ \"(pages-dir-browser)/./node_modules/svg-loaders-react/dist/index.js\");\n/* harmony import */ var svg_loaders_react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(svg_loaders_react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @core/components/ui/sidebar */ \"(pages-dir-browser)/./core/components/ui/sidebar.jsx\");\n/* harmony import */ var _core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @core/components/ui/breadcrumb */ \"(pages-dir-browser)/./core/components/ui/breadcrumb.jsx\");\n/* harmony import */ var _core_components_app_sidebar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @core/components/app-sidebar */ \"(pages-dir-browser)/./core/components/app-sidebar.jsx\");\n/* harmony import */ var _core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @core/components/ui/select */ \"(pages-dir-browser)/./core/components/ui/select.jsx\");\n/* harmony import */ var _core_components_ui_label__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @core/components/ui/label */ \"(pages-dir-browser)/./core/components/ui/label.jsx\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,ChevronsUpDown,CircleCheck!=!lucide-react */ \"(pages-dir-browser)/__barrel_optimize__?names=Check,ChevronLeft,ChevronRight,ChevronsUpDown,CircleCheck!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _core_lib_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @core/lib/utils */ \"(pages-dir-browser)/./core/lib/utils.js\");\n/* harmony import */ var _core_components_ui_button__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @core/components/ui/button */ \"(pages-dir-browser)/./core/components/ui/button.jsx\");\n/* harmony import */ var _core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @core/components/ui/command */ \"(pages-dir-browser)/./core/components/ui/command.jsx\");\n/* harmony import */ var _core_components_ui_popover__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @core/components/ui/popover */ \"(pages-dir-browser)/./core/components/ui/popover.jsx\");\n/* harmony import */ var core_TSkillsLib__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! core/TSkillsLib */ \"(pages-dir-browser)/./core/TSkillsLib.js\");\n/* harmony import */ var core_BSkillsLib__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! core/BSkillsLib */ \"(pages-dir-browser)/./core/BSkillsLib.js\");\n/* harmony import */ var core_Mapper_MapperSkillsLibHeader__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! core/Mapper/MapperSkillsLibHeader */ \"(pages-dir-browser)/./core/Mapper/MapperSkillsLibHeader.js\");\n/* harmony import */ var core_Mapper_MapperSkillRow__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! core/Mapper/MapperSkillRow */ \"(pages-dir-browser)/./core/Mapper/MapperSkillRow.js\");\n/* harmony import */ var core_MapperB_MapperBSkillsLibHeader__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! core/MapperB/MapperBSkillsLibHeader */ \"(pages-dir-browser)/./core/MapperB/MapperBSkillsLibHeader.js\");\n/* harmony import */ var core_MapperB_MapperBSkillRow__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! core/MapperB/MapperBSkillRow */ \"(pages-dir-browser)/./core/MapperB/MapperBSkillRow.js\");\n/* harmony import */ var _core_Toaster__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @core/Toaster */ \"(pages-dir-browser)/./core/Toaster.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar __N_SSG = true;\nfunction Dashboard(param) {\n    let { behavioural_skills, technical_skills } = param;\n    var _behavioural_skills_find, _technical_skills_find;\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userRole, setUserRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userRoles, setUserRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [typeSelected, setTypeSelected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [roleSelected, setRoleSelected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [skillSelected, setSkillSelected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [skillSelectedId, setSkillSelectedId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // const [value, setValue] = useState(null);\n    const [selectedLevel, setSelectedLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [existingMap, setExistingMap] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [allMappings, setAllMappings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            // Check for existing session\n            _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getSession().then({\n                \"Dashboard.useEffect\": (param)=>{\n                    let { data: { session } } = param;\n                    setSession(session);\n                    if (!session) {\n                        // Redirect to index page if no session\n                        router.push(\"/\");\n                    }\n                }\n            }[\"Dashboard.useEffect\"]);\n            // Listen for auth state changes\n            const { data: { subscription } } = _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.onAuthStateChange({\n                \"Dashboard.useEffect\": (_event, session)=>{\n                    setSession(session);\n                    if (!session) {\n                        // Redirect to index page if session is lost\n                        router.push(\"/\");\n                    }\n                }\n            }[\"Dashboard.useEffect\"]);\n            return ({\n                \"Dashboard.useEffect\": ()=>subscription.unsubscribe()\n            })[\"Dashboard.useEffect\"];\n        }\n    }[\"Dashboard.useEffect\"], [\n        router\n    ]);\n    // Fetch user profile when session is available\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if (session) {\n                getUserProfile();\n            }\n        }\n    }[\"Dashboard.useEffect\"], [\n        session\n    ]);\n    // useEffect(() => {\n    //   if (skillSelectedId) {\n    //     getMappings();\n    //   }\n    // }, [skillSelected]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if (roleSelected) {\n                getAllMappings(roleSelected);\n            }\n        }\n    }[\"Dashboard.useEffect\"], [\n        roleSelected,\n        skillSelected\n    ]);\n    /* --- DEBUG --- */ // console.log(\"existingMap\");\n    // console.log(existingMap);\n    console.log(\"typeSelected\");\n    console.log(typeSelected);\n    // console.log(\"allMappings\");\n    // console.log(allMappings);\n    // console.log(\"roleSelected\");\n    // console.log(roleSelected);\n    console.log(\"skillSelected\");\n    console.log(skillSelected);\n    // console.log(\"skillSelectedId\");\n    // console.log(skillSelectedId && skillSelectedId.id);\n    // console.log(\"selectedLevel\");\n    // console.log(selectedLevel);\n    // console.log(\"userData\");\n    // console.log(userData);\n    // console.log(\"userRole\");\n    // console.log(userRole);\n    // console.log(\"userRoles\");\n    // console.log(userRoles);\n    // console.log(\"behavioural_skills\");\n    // console.log(behavioural_skills);\n    // console.log(\"technical_skills\");\n    // console.log(technical_skills);\n    /* --- DEBUG --- */ const [lvl1, setLvl1] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lvl2, setLvl2] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lvl3, setLvl3] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lvl4, setLvl4] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lvl5, setLvl5] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // console.log(isChecked ? \"checked\" : \"unchecked\");\n    const handleOnChange = (level)=>{\n        // Reset all levels if the current level is being unchecked\n        if (level === 1 && lvl1 || level === 2 && lvl2 || level === 3 && lvl3 || level === 4 && lvl4 || level === 5 && lvl5) {\n            setLvl1(false);\n            setLvl2(false);\n            setLvl3(false);\n            setLvl4(false);\n            setLvl5(false);\n            return;\n        }\n        // Set all levels to false first\n        setLvl1(false);\n        setLvl2(false);\n        setLvl3(false);\n        setLvl4(false);\n        setLvl5(false);\n        // Then set only the selected level to true\n        switch(level){\n            case 1:\n                setLvl1(true);\n                setSelectedLevel(1);\n                break;\n            case 2:\n                setLvl2(true);\n                setSelectedLevel(2);\n                break;\n            case 3:\n                setLvl3(true);\n                setSelectedLevel(3);\n                break;\n            case 4:\n                setLvl4(true);\n                setSelectedLevel(4);\n                break;\n            case 5:\n                setLvl5(true);\n                setSelectedLevel(5);\n                break;\n        }\n    };\n    // select role\n    const selectRole = (roleSelected)=>{\n        console.log(\"roleSelected\");\n        setRoleSelected(roleSelected);\n    // setTypeSelected(null);\n    // setSkillSelectedId(null);\n    };\n    const showSaveConfirm = ()=>{\n        sonner__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Mapping created\", {\n            classNames: {\n                icon: \"!text-green-600\"\n            }\n        });\n    };\n    const showError = ()=>{\n        sonner__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Error - not saved, make sure level is selected\", {\n            classNames: {\n                icon: \"!text-red-600\"\n            }\n        });\n    };\n    const getSelectedRecord = (skill_id)=>{\n        // if behavioural or technical, get the record from the array\n        if (typeSelected === \"Behavioural\") {\n            const selectedSkillsRec = behavioural_skills.find((record)=>record.id === skill_id);\n            return selectedSkillsRec;\n        }\n        if (typeSelected === \"Technical\") {\n            const selectedSkillsRec = technical_skills.find((record)=>record.id === skill_id);\n            return selectedSkillsRec;\n        }\n    };\n    async function getUserProfile() {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"profiles\").select(\"*\").eq(\"id\", user.id).single();\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                setUserData(data);\n                getUserRole(data.team_role);\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    // get user's role\n    async function getUserRole(id) {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"roles\").select(\"id, team\").eq(\"id\", id).single();\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                setUserRole(data);\n                getRoles(data.team);\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    // get roles based on user\n    async function getRoles(team) {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"roles\").select(\"id, team, role_name\").eq(\"team\", team);\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                setUserRoles(data);\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    // get existing mappings based on user's team\n    async function getMappings(type) {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"role_mapping\").select().eq(\"skill_id\", skillSelectedId && skillSelectedId.id) // add role here too!\n            .eq(\"role_id\", roleSelected).eq(\"skill_type\", type).single();\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                console.log(\"existing data\");\n                console.log(data);\n                setExistingMap(data);\n                handleOnChange(data.skill_level);\n            } else {\n                console.log(\"no existing data\");\n                handleOnChange(null);\n                setSelectedLevel(null);\n                setExistingMap(null);\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    // get all existing mappings for the role\n    async function getAllMappings(roleId) {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"role_mapping\").select().eq(\"role_id\", roleId);\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                setAllMappings(data);\n            } else {\n                console.log(\"no existing data\");\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    // update existing or create new mapping\n    const createMappingRecord = ()=>{\n        if (existingMap) {\n            updateMapping(existingMap && existingMap.id);\n        } else {\n            createMapping();\n        }\n    };\n    // create mapping for currently selected skill\n    async function createMapping() {\n        try {\n            // setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"role_mapping\").insert({\n                role_id: roleSelected,\n                skill_type: typeSelected,\n                skill_id: skillSelectedId && skillSelectedId.id,\n                skill_level: selectedLevel,\n                skill_name: skillSelectedId && skillSelectedId.skill_name\n            });\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (status == 201) {\n                console.log(status);\n                showSaveConfirm();\n            }\n        } catch (error) {\n            console.log(\"Error:\", error.message);\n            showError(error.message);\n        } finally{\n        // setLoading(false);\n        }\n    }\n    async function updateMapping(id) {\n        try {\n            // setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"role_mapping\").update({\n                skill_level: selectedLevel\n            }).eq(\"id\", id);\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (status == 204) {\n                console.log(status);\n                showSaveConfirm();\n            }\n        } catch (error) {\n            console.log(\"Error:\", error.message);\n            showError(error.message);\n        } finally{\n        // setLoading(false);\n        }\n    }\n    // delete a mapping record\n    const deleteMappingRecord = (id)=>{\n        deleteMapping(id);\n    };\n    async function deleteMapping(id) {\n        try {\n            // setLoading(true);\n            let { error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"role_mapping\").delete().eq(\"id\", id);\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (status == 204) {\n                console.log(status);\n            // showSaveConfirm();\n            }\n        } catch (error) {\n            console.log(\"Error:\", error.message);\n            showError(error.message);\n        } finally{\n        // setLoading(false);\n        }\n    }\n    // Show loading state while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid place-items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-10 mb-10 flex flex-row space-x-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(svg_loaders_react__WEBPACK_IMPORTED_MODULE_5__.Oval, {\n                        stroke: \"#0c39ac\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                        lineNumber: 571,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                    lineNumber: 570,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                lineNumber: 569,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n            lineNumber: 568,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render dashboard if no session (will redirect)\n    if (!session) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_app_sidebar__WEBPACK_IMPORTED_MODULE_8__.AppSidebar, {\n                userData: userData,\n                behavioural_skills: behavioural_skills,\n                technical_skills: technical_skills,\n                allMappings: allMappings,\n                deleteMappingRecord: deleteMappingRecord,\n                typeSelected: typeSelected\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                lineNumber: 585,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarInset, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"flex h-16 shrink-0 items-center gap-2 border-b\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 px-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarTrigger, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                    lineNumber: 597,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__.Breadcrumb, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__.BreadcrumbList, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__.BreadcrumbItem, {\n                                                className: \"hidden md:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__.BreadcrumbLink, {\n                                                    href: \"#\",\n                                                    children: \"Admin tools\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                    lineNumber: 602,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                lineNumber: 601,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__.BreadcrumbSeparator, {\n                                                className: \"hidden md:block\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                lineNumber: 604,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__.BreadcrumbItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__.BreadcrumbLink, {\n                                                    children: \"Skills mapping\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                    lineNumber: 606,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                lineNumber: 605,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                        lineNumber: 600,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                    lineNumber: 599,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                            lineNumber: 596,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                        lineNumber: 595,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-1 flex-row gap-2 p-2 pt-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex m-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-6 pt-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                onValueChange: (roleSelected)=>selectRole(roleSelected),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                        className: \"text-sm text-center pb-1 text-primary font-semibold\",\n                                                        htmlFor: \"skill type\",\n                                                        children: \"Role\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 626,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                        className: \"w-[300px]\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                            placeholder: \"Select a role\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 633,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 632,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                                        children: userRoles && userRoles.map((role)=>{\n                                                            if (role.role_name !== \"Team Owner\") {\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                    value: role.id,\n                                                                    children: role.role_name\n                                                                }, role.id, false, {\n                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                    lineNumber: 640,\n                                                                    columnNumber: 29\n                                                                }, this);\n                                                            }\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 635,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                lineNumber: 620,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                            lineNumber: 619,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-6 pt-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                // onValueChange={(skillSelected) =>\n                                                //   setTypeSelected(skillSelected)\n                                                // }\n                                                // pass the type to the get mappings query\n                                                onValueChange: ((value1)=>getMappings(value1), setTypeSelected(value)),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                        className: \"text-sm text-center pb-1  text-primary font-semibold\",\n                                                        htmlFor: \"skill type\",\n                                                        children: \"Skill type\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 660,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                        className: \"w-[150px]\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                            placeholder: \"Select type\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 667,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 666,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                value: \"Behavioural\",\n                                                                children: \"Behavioural\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                lineNumber: 670,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                value: \"Technical\",\n                                                                children: \"Technical\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                lineNumber: 671,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 669,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                lineNumber: 651,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                            lineNumber: 650,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-6 pt-0\",\n                                            children: [\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex ml-3 pt-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_button__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__.ChevronLeft, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 679,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 678,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                    lineNumber: 677,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                            lineNumber: 675,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3 pt-0\",\n                                            children: [\n                                                !typeSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                    disabled: true,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                            className: \"text-sm text-center pb-1 text-primary font-semibold\",\n                                                            htmlFor: \"skill type\",\n                                                            children: \"Skill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 686,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                            className: \"w-[220px]\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                                placeholder: \"Select a skill\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                lineNumber: 693,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 692,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                    lineNumber: 685,\n                                                    columnNumber: 19\n                                                }, this),\n                                                typeSelected === \"Behavioural\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_popover__WEBPACK_IMPORTED_MODULE_14__.Popover, {\n                                                    open: open,\n                                                    onOpenChange: setOpen,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                            className: \"text-sm text-center pb-1 text-primary font-semibold\",\n                                                            htmlFor: \"skill type\",\n                                                            children: \"Skill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 700,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_popover__WEBPACK_IMPORTED_MODULE_14__.PopoverTrigger, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_button__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                                                variant: \"outline\",\n                                                                role: \"combobox\",\n                                                                \"aria-expanded\": open,\n                                                                className: \"w-[300px] justify-between text-muted-foreground\",\n                                                                children: [\n                                                                    skillSelected ? (_behavioural_skills_find = behavioural_skills.find((skill)=>skill.skill_name === skillSelected)) === null || _behavioural_skills_find === void 0 ? void 0 : _behavioural_skills_find.skill_name : \"Select skill\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__.ChevronsUpDown, {\n                                                                        className: \"opacity-50\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                        lineNumber: 718,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                lineNumber: 707,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 706,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_popover__WEBPACK_IMPORTED_MODULE_14__.PopoverContent, {\n                                                            className: \"w-[200px] p-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.Command, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandInput, {\n                                                                        placeholder: \"Search skills...\",\n                                                                        className: \"h-9\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                        lineNumber: 723,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandList, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandEmpty, {\n                                                                                children: \"No match found\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                lineNumber: 728,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandGroup, {\n                                                                                children: behavioural_skills.map((skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandItem, {\n                                                                                        value: skill.skill_name,\n                                                                                        onSelect: (currentValue)=>{\n                                                                                            setSkillSelected(currentValue === skillSelected ? \"\" : currentValue);\n                                                                                            setSkillSelectedId(getSelectedRecord(skill.id));\n                                                                                            setOpen(false);\n                                                                                        },\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__.CircleCheck, {\n                                                                                                className: allMappings.find((mapping)=>mapping.skill_id === skill.id) ? \"text-yellow-500\" : \"opacity-0\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                                lineNumber: 746,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            skill.skill_name,\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__.Check, {\n                                                                                                className: (0,_core_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"ml-auto\", skillSelected === skill.skill_name ? \"opacity-100\" : \"opacity-0\")\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                                lineNumber: 756,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, skill.skill_name, true, {\n                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                        lineNumber: 731,\n                                                                                        columnNumber: 31\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                lineNumber: 729,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                        lineNumber: 727,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                lineNumber: 722,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 721,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                    lineNumber: 699,\n                                                    columnNumber: 19\n                                                }, this),\n                                                typeSelected === \"Technical\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_popover__WEBPACK_IMPORTED_MODULE_14__.Popover, {\n                                                    open: open,\n                                                    onOpenChange: setOpen,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                            className: \"text-sm text-center pb-1 text-primary font-semibold\",\n                                                            htmlFor: \"skill type\",\n                                                            children: \"Skill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 775,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_popover__WEBPACK_IMPORTED_MODULE_14__.PopoverTrigger, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_button__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                                                variant: \"outline\",\n                                                                role: \"combobox\",\n                                                                \"aria-expanded\": open,\n                                                                className: \"w-[300px] justify-between\",\n                                                                children: [\n                                                                    skillSelected ? (_technical_skills_find = technical_skills.find((skill)=>skill.skill_name === skillSelected)) === null || _technical_skills_find === void 0 ? void 0 : _technical_skills_find.skill_name : \"Select skill\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__.ChevronsUpDown, {\n                                                                        className: \"opacity-50\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                        lineNumber: 793,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                lineNumber: 782,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 781,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_popover__WEBPACK_IMPORTED_MODULE_14__.PopoverContent, {\n                                                            className: \"w-[200px] p-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.Command, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandInput, {\n                                                                        placeholder: \"Search skills...\",\n                                                                        className: \"h-9\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                        lineNumber: 798,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandList, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandEmpty, {\n                                                                                children: \"No match found\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                lineNumber: 803,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandGroup, {\n                                                                                children: technical_skills.map((skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandItem, {\n                                                                                        value: skill.skill_name,\n                                                                                        onSelect: (currentValue)=>{\n                                                                                            setSkillSelected(currentValue === skillSelected ? \"\" : currentValue);\n                                                                                            setSkillSelectedId(getSelectedRecord(skill.id));\n                                                                                            setOpen(false);\n                                                                                        },\n                                                                                        children: [\n                                                                                            skill.skill_name,\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__.Check, {\n                                                                                                className: (0,_core_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"ml-auto\", skillSelected === skill.skill_name ? \"opacity-100\" : \"opacity-0\")\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                                lineNumber: 824,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, skill.skill_name, true, {\n                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                        lineNumber: 806,\n                                                                                        columnNumber: 31\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                lineNumber: 804,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                        lineNumber: 802,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                lineNumber: 797,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 796,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                    lineNumber: 774,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                            lineNumber: 683,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-0 pt-0\",\n                                            children: [\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex ml-3=0 pt-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_button__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                                        className: \"ml-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__.ChevronRight, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 845,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 844,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                    lineNumber: 843,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                            lineNumber: 841,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex ml-3 pt-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_button__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                                className: \"bg-green-600 hover:bg-green-600/90\",\n                                                onClick: createMappingRecord,\n                                                children: \"Save selection\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                lineNumber: 851,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                            lineNumber: 850,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                    lineNumber: 618,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                lineNumber: 617,\n                                columnNumber: 11\n                            }, this),\n                            skillSelected && typeSelected === \"Behavioural\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(core_BSkillsLib__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(core_MapperB_MapperBSkillsLibHeader__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                        lineNumber: 863,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(core_MapperB_MapperBSkillRow__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        behavioural_sub_skills: skillSelectedId.behavioural_sub_skills,\n                                        handleOnChange: handleOnChange,\n                                        lvl1: lvl1,\n                                        lvl2: lvl2,\n                                        lvl3: lvl3,\n                                        lvl4: lvl4,\n                                        lvl5: lvl5\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                        lineNumber: 864,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                lineNumber: 862,\n                                columnNumber: 13\n                            }, this),\n                            skillSelected && typeSelected === \"Technical\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(core_TSkillsLib__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(core_Mapper_MapperSkillsLibHeader__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                        lineNumber: 878,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(core_Mapper_MapperSkillRow__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        technical_sub_skills: skillSelectedId.technical_sub_skills,\n                                        handleOnChange: handleOnChange,\n                                        lvl1: lvl1,\n                                        lvl2: lvl2,\n                                        lvl3: lvl3,\n                                        lvl4: lvl4\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                        lineNumber: 879,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                lineNumber: 877,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_Toaster__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                position: \"bottom-right\",\n                                toastOptions: {\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                lineNumber: 889,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                        lineNumber: 612,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                lineNumber: 594,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n        lineNumber: 584,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"D6Nkesg9jSQK9AHzZzpX4SoWaOE=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/admin/skills-mapping.js\n"));

/***/ })

});