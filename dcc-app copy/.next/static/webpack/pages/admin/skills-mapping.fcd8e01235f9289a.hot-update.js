"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/admin/skills-mapping",{

/***/ "(pages-dir-browser)/./pages/admin/skills-mapping.js":
/*!***************************************!*\
  !*** ./pages/admin/skills-mapping.js ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSG: () => (/* binding */ __N_SSG),\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(pages-dir-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @utils/supabaseClient */ \"(pages-dir-browser)/./utils/supabaseClient.js\");\n/* harmony import */ var svg_loaders_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! svg-loaders-react */ \"(pages-dir-browser)/./node_modules/svg-loaders-react/dist/index.js\");\n/* harmony import */ var svg_loaders_react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(svg_loaders_react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @core/components/ui/sidebar */ \"(pages-dir-browser)/./core/components/ui/sidebar.jsx\");\n/* harmony import */ var _core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @core/components/ui/breadcrumb */ \"(pages-dir-browser)/./core/components/ui/breadcrumb.jsx\");\n/* harmony import */ var _core_components_app_sidebar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @core/components/app-sidebar */ \"(pages-dir-browser)/./core/components/app-sidebar.jsx\");\n/* harmony import */ var _core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @core/components/ui/select */ \"(pages-dir-browser)/./core/components/ui/select.jsx\");\n/* harmony import */ var _core_components_ui_label__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @core/components/ui/label */ \"(pages-dir-browser)/./core/components/ui/label.jsx\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,ChevronsUpDown,CircleCheck!=!lucide-react */ \"(pages-dir-browser)/__barrel_optimize__?names=Check,ChevronLeft,ChevronRight,ChevronsUpDown,CircleCheck!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _core_lib_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @core/lib/utils */ \"(pages-dir-browser)/./core/lib/utils.js\");\n/* harmony import */ var _core_components_ui_button__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @core/components/ui/button */ \"(pages-dir-browser)/./core/components/ui/button.jsx\");\n/* harmony import */ var _core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @core/components/ui/command */ \"(pages-dir-browser)/./core/components/ui/command.jsx\");\n/* harmony import */ var _core_components_ui_popover__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @core/components/ui/popover */ \"(pages-dir-browser)/./core/components/ui/popover.jsx\");\n/* harmony import */ var core_TSkillsLib__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! core/TSkillsLib */ \"(pages-dir-browser)/./core/TSkillsLib.js\");\n/* harmony import */ var core_BSkillsLib__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! core/BSkillsLib */ \"(pages-dir-browser)/./core/BSkillsLib.js\");\n/* harmony import */ var core_Mapper_MapperSkillsLibHeader__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! core/Mapper/MapperSkillsLibHeader */ \"(pages-dir-browser)/./core/Mapper/MapperSkillsLibHeader.js\");\n/* harmony import */ var core_Mapper_MapperSkillRow__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! core/Mapper/MapperSkillRow */ \"(pages-dir-browser)/./core/Mapper/MapperSkillRow.js\");\n/* harmony import */ var core_MapperB_MapperBSkillsLibHeader__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! core/MapperB/MapperBSkillsLibHeader */ \"(pages-dir-browser)/./core/MapperB/MapperBSkillsLibHeader.js\");\n/* harmony import */ var core_MapperB_MapperBSkillRow__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! core/MapperB/MapperBSkillRow */ \"(pages-dir-browser)/./core/MapperB/MapperBSkillRow.js\");\n/* harmony import */ var _core_Toaster__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @core/Toaster */ \"(pages-dir-browser)/./core/Toaster.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar __N_SSG = true;\nfunction Dashboard(param) {\n    let { behavioural_skills, technical_skills } = param;\n    var _behavioural_skills_find, _technical_skills_find;\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userRole, setUserRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userRoles, setUserRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [typeSelected, setTypeSelected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [roleSelected, setRoleSelected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [skillSelected, setSkillSelected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [skillSelectedId, setSkillSelectedId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [value, setValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedLevel, setSelectedLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [existingMap, setExistingMap] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [allMappings, setAllMappings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            // Check for existing session\n            _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getSession().then({\n                \"Dashboard.useEffect\": (param)=>{\n                    let { data: { session } } = param;\n                    setSession(session);\n                    if (!session) {\n                        // Redirect to index page if no session\n                        router.push(\"/\");\n                    }\n                }\n            }[\"Dashboard.useEffect\"]);\n            // Listen for auth state changes\n            const { data: { subscription } } = _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.onAuthStateChange({\n                \"Dashboard.useEffect\": (_event, session)=>{\n                    setSession(session);\n                    if (!session) {\n                        // Redirect to index page if session is lost\n                        router.push(\"/\");\n                    }\n                }\n            }[\"Dashboard.useEffect\"]);\n            return ({\n                \"Dashboard.useEffect\": ()=>subscription.unsubscribe()\n            })[\"Dashboard.useEffect\"];\n        }\n    }[\"Dashboard.useEffect\"], [\n        router\n    ]);\n    // Fetch user profile when session is available\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if (session) {\n                getUserProfile();\n            }\n        }\n    }[\"Dashboard.useEffect\"], [\n        session\n    ]);\n    // useEffect(() => {\n    //   if (skillSelectedId) {\n    //     getMappings();\n    //   }\n    // }, [skillSelected]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if (roleSelected) {\n                getAllMappings(roleSelected);\n            }\n        }\n    }[\"Dashboard.useEffect\"], [\n        roleSelected,\n        skillSelected\n    ]);\n    // useEffect(() => {\n    //   if (skillSelected) {\n    //     getAllMappings(roleSelected, typeSelected);\n    //   }\n    // }, [skillSelected]);\n    /* --- DEBUG --- */ // console.log(\"existingMap\");\n    // console.log(existingMap);\n    console.log(\"roleSelected\");\n    console.log(roleSelected);\n    console.log(\"typeSelected\");\n    console.log(typeSelected);\n    console.log(\"skillSelected\");\n    console.log(skillSelected);\n    console.log(\"allMappings\");\n    console.log(allMappings);\n    // console.log(\"skillSelectedId\");\n    // console.log(skillSelectedId && skillSelectedId.id);\n    // console.log(\"selectedLevel\");\n    // console.log(selectedLevel);\n    // console.log(\"userData\");\n    // console.log(userData);\n    // console.log(\"userRole\");\n    // console.log(userRole);\n    // console.log(\"userRoles\");\n    // console.log(userRoles);\n    // console.log(\"behavioural_skills\");\n    // console.log(behavioural_skills);\n    // console.log(\"technical_skills\");\n    // console.log(technical_skills);\n    /* --- DEBUG --- */ const [lvl1, setLvl1] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lvl2, setLvl2] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lvl3, setLvl3] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lvl4, setLvl4] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lvl5, setLvl5] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // console.log(isChecked ? \"checked\" : \"unchecked\");\n    const handleOnChange = (level)=>{\n        // Reset all levels if the current level is being unchecked\n        if (level === 1 && lvl1 || level === 2 && lvl2 || level === 3 && lvl3 || level === 4 && lvl4 || level === 5 && lvl5) {\n            setLvl1(false);\n            setLvl2(false);\n            setLvl3(false);\n            setLvl4(false);\n            setLvl5(false);\n            return;\n        }\n        // Set all levels to false first\n        setLvl1(false);\n        setLvl2(false);\n        setLvl3(false);\n        setLvl4(false);\n        setLvl5(false);\n        // Then set only the selected level to true\n        switch(level){\n            case 1:\n                setLvl1(true);\n                setSelectedLevel(1);\n                break;\n            case 2:\n                setLvl2(true);\n                setSelectedLevel(2);\n                break;\n            case 3:\n                setLvl3(true);\n                setSelectedLevel(3);\n                break;\n            case 4:\n                setLvl4(true);\n                setSelectedLevel(4);\n                break;\n            case 5:\n                setLvl5(true);\n                setSelectedLevel(5);\n                break;\n        }\n    };\n    // select role\n    const selectRole = (roleSelected)=>{\n        console.log(\"roleSelected\");\n        setRoleSelected(roleSelected);\n    // setTypeSelected(null);\n    // setSkillSelectedId(null);\n    };\n    const showSaveConfirm = ()=>{\n        sonner__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Mapping created\", {\n            classNames: {\n                icon: \"!text-green-600\"\n            }\n        });\n    };\n    const showError = ()=>{\n        sonner__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Error - not saved, make sure level is selected\", {\n            classNames: {\n                icon: \"!text-red-600\"\n            }\n        });\n    };\n    const getSelectedRecord = (skill_id)=>{\n        // if behavioural or technical, get the record from the array\n        if (typeSelected === \"Behavioural\") {\n            const selectedSkillsRec = behavioural_skills.find((record)=>record.id === skill_id);\n            return selectedSkillsRec;\n        }\n        if (typeSelected === \"Technical\") {\n            const selectedSkillsRec = technical_skills.find((record)=>record.id === skill_id);\n            return selectedSkillsRec;\n        }\n    };\n    async function getUserProfile() {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"profiles\").select(\"*\").eq(\"id\", user.id).single();\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                setUserData(data);\n                getUserRole(data.team_role);\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    // get user's role\n    async function getUserRole(id) {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"roles\").select(\"id, team\").eq(\"id\", id).single();\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                setUserRole(data);\n                getRoles(data.team);\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    // get roles based on user\n    async function getRoles(team) {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"roles\").select(\"id, team, role_name\").eq(\"team\", team);\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                setUserRoles(data);\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    // get existing mappings based on user's team\n    async function getMappings(type) {\n        setTypeSelected(type);\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"role_mapping\").select().eq(\"skill_id\", skillSelectedId && skillSelectedId.id) // add role here too!\n            .eq(\"role_id\", roleSelected).eq(\"skill_type\", type).single();\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                console.log(\"existing data\");\n                console.log(data);\n                setExistingMap(data);\n                handleOnChange(data.skill_level);\n            } else {\n                console.log(\"no existing data\");\n                handleOnChange(null);\n                setSelectedLevel(null);\n                setExistingMap(null);\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    // get all existing mappings for the role\n    async function getAllMappings(roleId, type) {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"role_mapping\").select().eq(\"role_id\", roleId).eq(\"skill_type\", type);\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                console.log(\"All mappings\");\n                console.log(data);\n            } else {\n                console.log(\"no existing data\");\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    // update existing or create new mapping\n    const createMappingRecord = ()=>{\n        if (existingMap) {\n            updateMapping(existingMap && existingMap.id);\n        } else {\n            createMapping();\n        }\n    };\n    // create mapping for currently selected skill\n    async function createMapping() {\n        try {\n            // setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"role_mapping\").insert({\n                role_id: roleSelected,\n                skill_type: typeSelected,\n                skill_id: skillSelectedId && skillSelectedId.id,\n                skill_level: selectedLevel,\n                skill_name: skillSelectedId && skillSelectedId.skill_name\n            });\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (status == 201) {\n                console.log(status);\n                showSaveConfirm();\n            }\n        } catch (error) {\n            console.log(\"Error:\", error.message);\n            showError(error.message);\n        } finally{\n        // setLoading(false);\n        }\n    }\n    async function updateMapping(id) {\n        try {\n            // setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"role_mapping\").update({\n                skill_level: selectedLevel\n            }).eq(\"id\", id);\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (status == 204) {\n                console.log(status);\n                showSaveConfirm();\n            }\n        } catch (error) {\n            console.log(\"Error:\", error.message);\n            showError(error.message);\n        } finally{\n        // setLoading(false);\n        }\n    }\n    // delete a mapping record\n    const deleteMappingRecord = (id)=>{\n        deleteMapping(id);\n    };\n    async function deleteMapping(id) {\n        try {\n            // setLoading(true);\n            let { error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"role_mapping\").delete().eq(\"id\", id);\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (status == 204) {\n                console.log(status);\n            // showSaveConfirm();\n            }\n        } catch (error) {\n            console.log(\"Error:\", error.message);\n            showError(error.message);\n        } finally{\n        // setLoading(false);\n        }\n    }\n    // Show loading state while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid place-items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-10 mb-10 flex flex-row space-x-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(svg_loaders_react__WEBPACK_IMPORTED_MODULE_5__.Oval, {\n                        stroke: \"#0c39ac\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                        lineNumber: 580,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                    lineNumber: 579,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                lineNumber: 578,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n            lineNumber: 577,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render dashboard if no session (will redirect)\n    if (!session) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_app_sidebar__WEBPACK_IMPORTED_MODULE_8__.AppSidebar, {\n                userData: userData,\n                behavioural_skills: behavioural_skills,\n                technical_skills: technical_skills,\n                allMappings: allMappings,\n                deleteMappingRecord: deleteMappingRecord,\n                typeSelected: typeSelected\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                lineNumber: 594,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarInset, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"flex h-16 shrink-0 items-center gap-2 border-b\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 px-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarTrigger, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                    lineNumber: 606,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__.Breadcrumb, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__.BreadcrumbList, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__.BreadcrumbItem, {\n                                                className: \"hidden md:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__.BreadcrumbLink, {\n                                                    href: \"#\",\n                                                    children: \"Admin tools\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                    lineNumber: 611,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                lineNumber: 610,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__.BreadcrumbSeparator, {\n                                                className: \"hidden md:block\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                lineNumber: 613,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__.BreadcrumbItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__.BreadcrumbLink, {\n                                                    children: \"Skills mapping\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                    lineNumber: 615,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                lineNumber: 614,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                        lineNumber: 609,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                    lineNumber: 608,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                            lineNumber: 605,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                        lineNumber: 604,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-1 flex-row gap-2 p-2 pt-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex m-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-6 pt-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                onValueChange: (roleSelected)=>selectRole(roleSelected),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                        className: \"text-sm text-center pb-1 text-primary font-semibold\",\n                                                        htmlFor: \"skill type\",\n                                                        children: \"Role\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 635,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                        className: \"w-[300px]\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                            placeholder: \"Select a role\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 642,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 641,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                                        children: userRoles && userRoles.map((role)=>{\n                                                            if (role.role_name !== \"Team Owner\") {\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                    value: role.id,\n                                                                    children: role.role_name\n                                                                }, role.id, false, {\n                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                    lineNumber: 649,\n                                                                    columnNumber: 29\n                                                                }, this);\n                                                            }\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 644,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                lineNumber: 629,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                            lineNumber: 628,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-6 pt-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                // onValueChange={(skillSelected) =>\n                                                //   setTypeSelected(skillSelected)\n                                                // }\n                                                // pass the type to the get mappings query\n                                                onValueChange: (value)=>getMappings(value),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                        className: \"text-sm text-center pb-1  text-primary font-semibold\",\n                                                        htmlFor: \"skill type\",\n                                                        children: \"Skill type\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 667,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                        className: \"w-[150px]\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                            placeholder: \"Select type\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 674,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 673,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                value: \"Behavioural\",\n                                                                children: \"Behavioural\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                lineNumber: 677,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                value: \"Technical\",\n                                                                children: \"Technical\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                lineNumber: 678,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 676,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                lineNumber: 660,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                            lineNumber: 659,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-6 pt-0\",\n                                            children: [\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex ml-3 pt-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_button__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__.ChevronLeft, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 686,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 685,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                    lineNumber: 684,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                            lineNumber: 682,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3 pt-0\",\n                                            children: [\n                                                !typeSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                    disabled: true,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                            className: \"text-sm text-center pb-1 text-primary font-semibold\",\n                                                            htmlFor: \"skill type\",\n                                                            children: \"Skill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 693,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                            className: \"w-[220px]\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                                placeholder: \"Select a skill\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                lineNumber: 700,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 699,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                    lineNumber: 692,\n                                                    columnNumber: 19\n                                                }, this),\n                                                typeSelected === \"Behavioural\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_popover__WEBPACK_IMPORTED_MODULE_14__.Popover, {\n                                                    open: open,\n                                                    onOpenChange: setOpen,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                            className: \"text-sm text-center pb-1 text-primary font-semibold\",\n                                                            htmlFor: \"skill type\",\n                                                            children: \"Skill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 707,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_popover__WEBPACK_IMPORTED_MODULE_14__.PopoverTrigger, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_button__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                                                variant: \"outline\",\n                                                                role: \"combobox\",\n                                                                \"aria-expanded\": open,\n                                                                className: \"w-[300px] justify-between text-muted-foreground\",\n                                                                children: [\n                                                                    skillSelected ? (_behavioural_skills_find = behavioural_skills.find((skill)=>skill.skill_name === skillSelected)) === null || _behavioural_skills_find === void 0 ? void 0 : _behavioural_skills_find.skill_name : \"Select skill\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__.ChevronsUpDown, {\n                                                                        className: \"opacity-50\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                        lineNumber: 725,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                lineNumber: 714,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 713,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_popover__WEBPACK_IMPORTED_MODULE_14__.PopoverContent, {\n                                                            className: \"w-[200px] p-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.Command, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandInput, {\n                                                                        placeholder: \"Search skills...\",\n                                                                        className: \"h-9\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                        lineNumber: 730,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandList, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandEmpty, {\n                                                                            children: \"No match found\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                            lineNumber: 735,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                        lineNumber: 734,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                lineNumber: 729,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 728,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                    lineNumber: 706,\n                                                    columnNumber: 19\n                                                }, this),\n                                                typeSelected === \"Technical\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_popover__WEBPACK_IMPORTED_MODULE_14__.Popover, {\n                                                    open: open,\n                                                    onOpenChange: setOpen,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                            className: \"text-sm text-center pb-1 text-primary font-semibold\",\n                                                            htmlFor: \"skill type\",\n                                                            children: \"Skill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 784,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_popover__WEBPACK_IMPORTED_MODULE_14__.PopoverTrigger, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_button__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                                                variant: \"outline\",\n                                                                role: \"combobox\",\n                                                                \"aria-expanded\": open,\n                                                                className: \"w-[300px] justify-between\",\n                                                                children: [\n                                                                    skillSelected ? (_technical_skills_find = technical_skills.find((skill)=>skill.skill_name === skillSelected)) === null || _technical_skills_find === void 0 ? void 0 : _technical_skills_find.skill_name : \"Select skill\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__.ChevronsUpDown, {\n                                                                        className: \"opacity-50\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                        lineNumber: 802,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                lineNumber: 791,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 790,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_popover__WEBPACK_IMPORTED_MODULE_14__.PopoverContent, {\n                                                            className: \"w-[200px] p-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.Command, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandInput, {\n                                                                        placeholder: \"Search skills...\",\n                                                                        className: \"h-9\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                        lineNumber: 807,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandList, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandEmpty, {\n                                                                                children: \"No match found\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                lineNumber: 812,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandGroup, {\n                                                                                children: technical_skills.map((skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandItem, {\n                                                                                        value: skill.skill_name,\n                                                                                        onSelect: (currentValue)=>{\n                                                                                            setSkillSelected(currentValue === skillSelected ? \"\" : currentValue);\n                                                                                            setSkillSelectedId(getSelectedRecord(skill.id));\n                                                                                            setOpen(false);\n                                                                                        },\n                                                                                        children: [\n                                                                                            skill.skill_name,\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__.Check, {\n                                                                                                className: (0,_core_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"ml-auto\", skillSelected === skill.skill_name ? \"opacity-100\" : \"opacity-0\")\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                                lineNumber: 833,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, skill.skill_name, true, {\n                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                        lineNumber: 815,\n                                                                                        columnNumber: 31\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                lineNumber: 813,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                        lineNumber: 811,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                lineNumber: 806,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 805,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                    lineNumber: 783,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                            lineNumber: 690,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-0 pt-0\",\n                                            children: [\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex ml-3=0 pt-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_button__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                                        className: \"ml-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__.ChevronRight, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 854,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 853,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                    lineNumber: 852,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                            lineNumber: 850,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex ml-3 pt-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_button__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                                className: \"bg-green-600 hover:bg-green-600/90\",\n                                                onClick: createMappingRecord,\n                                                children: \"Save selection\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                lineNumber: 860,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                            lineNumber: 859,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                    lineNumber: 627,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                lineNumber: 626,\n                                columnNumber: 11\n                            }, this),\n                            skillSelected && typeSelected === \"Behavioural\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(core_BSkillsLib__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(core_MapperB_MapperBSkillsLibHeader__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                        lineNumber: 872,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(core_MapperB_MapperBSkillRow__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        behavioural_sub_skills: skillSelectedId.behavioural_sub_skills,\n                                        handleOnChange: handleOnChange,\n                                        lvl1: lvl1,\n                                        lvl2: lvl2,\n                                        lvl3: lvl3,\n                                        lvl4: lvl4,\n                                        lvl5: lvl5\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                        lineNumber: 873,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                lineNumber: 871,\n                                columnNumber: 13\n                            }, this),\n                            skillSelected && typeSelected === \"Technical\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(core_TSkillsLib__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(core_Mapper_MapperSkillsLibHeader__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                        lineNumber: 887,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(core_Mapper_MapperSkillRow__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        technical_sub_skills: skillSelectedId.technical_sub_skills,\n                                        handleOnChange: handleOnChange,\n                                        lvl1: lvl1,\n                                        lvl2: lvl2,\n                                        lvl3: lvl3,\n                                        lvl4: lvl4\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                        lineNumber: 888,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                lineNumber: 886,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_Toaster__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                position: \"bottom-right\",\n                                toastOptions: {\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                lineNumber: 898,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                        lineNumber: 621,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                lineNumber: 603,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n        lineNumber: 593,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"gldjzi+fY/08eNFn7LFFxJAkRSI=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/admin/skills-mapping.js\n"));

/***/ })

});