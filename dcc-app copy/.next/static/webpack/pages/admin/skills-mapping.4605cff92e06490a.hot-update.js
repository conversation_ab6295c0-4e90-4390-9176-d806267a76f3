"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/admin/skills-mapping",{

/***/ "(pages-dir-browser)/./core/components/ui/nav-admin.jsx":
/*!******************************************!*\
  !*** ./core/components/ui/nav-admin.jsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NavAdmin: () => (/* binding */ NavAdmin)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronRight_Combine_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,Combine!=!lucide-react */ \"(pages-dir-browser)/__barrel_optimize__?names=ChevronRight,Combine!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _core_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @core/components/ui/collapsible */ \"(pages-dir-browser)/./core/components/ui/collapsible.jsx\");\n/* harmony import */ var _core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @core/components/ui/sidebar */ \"(pages-dir-browser)/./core/components/ui/sidebar.jsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(pages-dir-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ NavAdmin auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction NavAdmin(param) {\n    let { userData, allMappings, deleteMappingRecord, typeSelected, selected_item } = param;\n    _s();\n    const [skills, setskills] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(allMappings);\n    const removeItem = (id)=>{\n        const updatedSkills = [\n            ...skills\n        ];\n        const index = updatedSkills.findIndex((skill)=>skill.id === id);\n        if (index !== -1) {\n            updatedSkills.splice(index, 1);\n        }\n        setskills(updatedSkills);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarGroup, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarGroupLabel, {\n                className: \"min-w-8 bg-secondary text-primary-foreground\",\n                children: \"Admin tools\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenu, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_2__.Collapsible, {\n                    asChild: true,\n                    defaultOpen: false,\n                    className: \"group/collapsible\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenuItem, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_2__.CollapsibleTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenuButton, {\n                                    tooltip: \"Admin tools\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_Combine_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Combine, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                            href: \"/admin/skills-mapping\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Skills mapping\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                                    lineNumber: 58,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" \"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_Combine_lucide_react__WEBPACK_IMPORTED_MODULE_5__.ChevronRight, {\n                                            className: \"ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_2__.CollapsibleContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenuSub, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenuSubItem, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenuSubButton, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                href: \"/behavioural/\",\n                                                children: selected_item && selected_item === \"behavioural\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-primary font-bold\",\n                                                    children: \"Behavioural\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 25\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Behavioural\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, \"behavioural\", false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this),\n                            typeSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-sm font-extrabold tracking-tight text-balance text-dccblue pb-2\",\n                                            children: [\n                                                \"Skills mapped:\",\n                                                \" \",\n                                                allMappings && allMappings.length + \" / \" + \" 12\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: skills && skills.map((skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                id: skill.id,\n                                                class: \" mb-1 relative rounded-md flex bg-dccdarkgrey py-0.5 pl-2.5 pr-8 border border-transparent text-sm text-white transition-all shadow-sm font-semibold\",\n                                                children: [\n                                                    skill.skill_name,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"flex items-center justify-center transition-all p-1 rounded-md text-white hover:bg-white/10 active:bg-white/10 absolute top-0.5 right-0.5\",\n                                                        type: \"button\",\n                                                        onClick: ()=>{\n                                                            deleteMappingRecord(skill.id);\n                                                            removeItem(skill.id);\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            viewBox: \"0 0 16 16\",\n                                                            fill: \"currentColor\",\n                                                            class: \"w-4 h-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M5.28 4.22a.75.75 0 0 0-1.06 1.06L6.94 8l-2.72 2.72a.75.75 0 1 0 1.06 1.06L8 9.06l2.72 2.72a.75.75 0 1 0 1.06-1.06L9.06 8l2.72-2.72a.75.75 0 0 0-1.06-1.06L8 6.94 5.28 4.22Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                                                lineNumber: 117,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                                            lineNumber: 111,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                                        lineNumber: 103,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                                lineNumber: 88,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                        lineNumber: 53,\n                        columnNumber: 11\n                    }, this)\n                }, 1, false, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-admin.jsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n_s(NavAdmin, \"Dnbtuq+ayHWYdqRvHVFL/OXsZ6Y=\");\n_c = NavAdmin;\nvar _c;\n$RefreshReg$(_c, \"NavAdmin\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./core/components/ui/nav-admin.jsx\n"));

/***/ })

});