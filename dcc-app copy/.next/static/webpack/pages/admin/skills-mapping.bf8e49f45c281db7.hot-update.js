"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/admin/skills-mapping",{

/***/ "(pages-dir-browser)/./pages/admin/skills-mapping.js":
/*!***************************************!*\
  !*** ./pages/admin/skills-mapping.js ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSG: () => (/* binding */ __N_SSG),\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(pages-dir-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @utils/supabaseClient */ \"(pages-dir-browser)/./utils/supabaseClient.js\");\n/* harmony import */ var svg_loaders_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! svg-loaders-react */ \"(pages-dir-browser)/./node_modules/svg-loaders-react/dist/index.js\");\n/* harmony import */ var svg_loaders_react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(svg_loaders_react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @core/components/ui/sidebar */ \"(pages-dir-browser)/./core/components/ui/sidebar.jsx\");\n/* harmony import */ var _core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @core/components/ui/breadcrumb */ \"(pages-dir-browser)/./core/components/ui/breadcrumb.jsx\");\n/* harmony import */ var _core_components_app_sidebar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @core/components/app-sidebar */ \"(pages-dir-browser)/./core/components/app-sidebar.jsx\");\n/* harmony import */ var _core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @core/components/ui/select */ \"(pages-dir-browser)/./core/components/ui/select.jsx\");\n/* harmony import */ var _core_components_ui_label__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @core/components/ui/label */ \"(pages-dir-browser)/./core/components/ui/label.jsx\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,ChevronsUpDown,CircleCheck!=!lucide-react */ \"(pages-dir-browser)/__barrel_optimize__?names=Check,ChevronLeft,ChevronRight,ChevronsUpDown,CircleCheck!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _core_lib_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @core/lib/utils */ \"(pages-dir-browser)/./core/lib/utils.js\");\n/* harmony import */ var _core_components_ui_button__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @core/components/ui/button */ \"(pages-dir-browser)/./core/components/ui/button.jsx\");\n/* harmony import */ var _core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @core/components/ui/command */ \"(pages-dir-browser)/./core/components/ui/command.jsx\");\n/* harmony import */ var _core_components_ui_popover__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @core/components/ui/popover */ \"(pages-dir-browser)/./core/components/ui/popover.jsx\");\n/* harmony import */ var core_TSkillsLib__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! core/TSkillsLib */ \"(pages-dir-browser)/./core/TSkillsLib.js\");\n/* harmony import */ var core_BSkillsLib__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! core/BSkillsLib */ \"(pages-dir-browser)/./core/BSkillsLib.js\");\n/* harmony import */ var core_Mapper_MapperSkillsLibHeader__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! core/Mapper/MapperSkillsLibHeader */ \"(pages-dir-browser)/./core/Mapper/MapperSkillsLibHeader.js\");\n/* harmony import */ var core_Mapper_MapperSkillRow__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! core/Mapper/MapperSkillRow */ \"(pages-dir-browser)/./core/Mapper/MapperSkillRow.js\");\n/* harmony import */ var core_MapperB_MapperBSkillsLibHeader__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! core/MapperB/MapperBSkillsLibHeader */ \"(pages-dir-browser)/./core/MapperB/MapperBSkillsLibHeader.js\");\n/* harmony import */ var core_MapperB_MapperBSkillRow__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! core/MapperB/MapperBSkillRow */ \"(pages-dir-browser)/./core/MapperB/MapperBSkillRow.js\");\n/* harmony import */ var _core_Toaster__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @core/Toaster */ \"(pages-dir-browser)/./core/Toaster.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar __N_SSG = true;\nfunction Dashboard(param) {\n    let { behavioural_skills, technical_skills } = param;\n    var _behavioural_skills_find, _technical_skills_find;\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userRole, setUserRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userRoles, setUserRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [typeSelected, setTypeSelected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [roleSelected, setRoleSelected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [skillSelected, setSkillSelected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [skillSelectedId, setSkillSelectedId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [value, setValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedLevel, setSelectedLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [existingMap, setExistingMap] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [allMappings, setAllMappings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            // Check for existing session\n            _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getSession().then({\n                \"Dashboard.useEffect\": (param)=>{\n                    let { data: { session } } = param;\n                    setSession(session);\n                    if (!session) {\n                        // Redirect to index page if no session\n                        router.push(\"/\");\n                    }\n                }\n            }[\"Dashboard.useEffect\"]);\n            // Listen for auth state changes\n            const { data: { subscription } } = _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.onAuthStateChange({\n                \"Dashboard.useEffect\": (_event, session)=>{\n                    setSession(session);\n                    if (!session) {\n                        // Redirect to index page if session is lost\n                        router.push(\"/\");\n                    }\n                }\n            }[\"Dashboard.useEffect\"]);\n            return ({\n                \"Dashboard.useEffect\": ()=>subscription.unsubscribe()\n            })[\"Dashboard.useEffect\"];\n        }\n    }[\"Dashboard.useEffect\"], [\n        router\n    ]);\n    // Fetch user profile when session is available\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if (session) {\n                getUserProfile();\n            }\n        }\n    }[\"Dashboard.useEffect\"], [\n        session\n    ]);\n    // useEffect(() => {\n    //   if (skillSelectedId) {\n    //     getMappings();\n    //   }\n    // }, [skillSelected]);\n    // useEffect(() => {\n    //   if (roleSelected) {\n    //     getAllMappings(roleSelected);\n    //   }\n    // }, [roleSelected, skillSelected]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if (skil) {\n                getAllMappings(roleSelected);\n            }\n        }\n    }[\"Dashboard.useEffect\"], [\n        roleSelected,\n        skillSelected\n    ]);\n    /* --- DEBUG --- */ // console.log(\"existingMap\");\n    // console.log(existingMap);\n    console.log(\"typeSelected\");\n    console.log(typeSelected);\n    // console.log(\"allMappings\");\n    // console.log(allMappings);\n    // console.log(\"roleSelected\");\n    // console.log(roleSelected);\n    console.log(\"skillSelected\");\n    console.log(skillSelected);\n    // console.log(\"skillSelectedId\");\n    // console.log(skillSelectedId && skillSelectedId.id);\n    // console.log(\"selectedLevel\");\n    // console.log(selectedLevel);\n    // console.log(\"userData\");\n    // console.log(userData);\n    // console.log(\"userRole\");\n    // console.log(userRole);\n    // console.log(\"userRoles\");\n    // console.log(userRoles);\n    // console.log(\"behavioural_skills\");\n    // console.log(behavioural_skills);\n    // console.log(\"technical_skills\");\n    // console.log(technical_skills);\n    /* --- DEBUG --- */ const [lvl1, setLvl1] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lvl2, setLvl2] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lvl3, setLvl3] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lvl4, setLvl4] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lvl5, setLvl5] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // console.log(isChecked ? \"checked\" : \"unchecked\");\n    const handleOnChange = (level)=>{\n        // Reset all levels if the current level is being unchecked\n        if (level === 1 && lvl1 || level === 2 && lvl2 || level === 3 && lvl3 || level === 4 && lvl4 || level === 5 && lvl5) {\n            setLvl1(false);\n            setLvl2(false);\n            setLvl3(false);\n            setLvl4(false);\n            setLvl5(false);\n            return;\n        }\n        // Set all levels to false first\n        setLvl1(false);\n        setLvl2(false);\n        setLvl3(false);\n        setLvl4(false);\n        setLvl5(false);\n        // Then set only the selected level to true\n        switch(level){\n            case 1:\n                setLvl1(true);\n                setSelectedLevel(1);\n                break;\n            case 2:\n                setLvl2(true);\n                setSelectedLevel(2);\n                break;\n            case 3:\n                setLvl3(true);\n                setSelectedLevel(3);\n                break;\n            case 4:\n                setLvl4(true);\n                setSelectedLevel(4);\n                break;\n            case 5:\n                setLvl5(true);\n                setSelectedLevel(5);\n                break;\n        }\n    };\n    // select role\n    const selectRole = (roleSelected)=>{\n        console.log(\"roleSelected\");\n        setRoleSelected(roleSelected);\n    // setTypeSelected(null);\n    // setSkillSelectedId(null);\n    };\n    const showSaveConfirm = ()=>{\n        sonner__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Mapping created\", {\n            classNames: {\n                icon: \"!text-green-600\"\n            }\n        });\n    };\n    const showError = ()=>{\n        sonner__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Error - not saved, make sure level is selected\", {\n            classNames: {\n                icon: \"!text-red-600\"\n            }\n        });\n    };\n    const getSelectedRecord = (skill_id)=>{\n        // if behavioural or technical, get the record from the array\n        if (typeSelected === \"Behavioural\") {\n            const selectedSkillsRec = behavioural_skills.find((record)=>record.id === skill_id);\n            return selectedSkillsRec;\n        }\n        if (typeSelected === \"Technical\") {\n            const selectedSkillsRec = technical_skills.find((record)=>record.id === skill_id);\n            return selectedSkillsRec;\n        }\n    };\n    async function getUserProfile() {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"profiles\").select(\"*\").eq(\"id\", user.id).single();\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                setUserData(data);\n                getUserRole(data.team_role);\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    // get user's role\n    async function getUserRole(id) {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"roles\").select(\"id, team\").eq(\"id\", id).single();\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                setUserRole(data);\n                getRoles(data.team);\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    // get roles based on user\n    async function getRoles(team) {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"roles\").select(\"id, team, role_name\").eq(\"team\", team);\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                setUserRoles(data);\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    // get existing mappings based on user's team\n    async function getMappings(type) {\n        setTypeSelected(type);\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"role_mapping\").select().eq(\"skill_id\", skillSelectedId && skillSelectedId.id) // add role here too!\n            .eq(\"role_id\", roleSelected).eq(\"skill_type\", type).single();\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                console.log(\"existing data\");\n                console.log(data);\n                setExistingMap(data);\n                handleOnChange(data.skill_level);\n            } else {\n                console.log(\"no existing data\");\n                handleOnChange(null);\n                setSelectedLevel(null);\n                setExistingMap(null);\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    // get all existing mappings for the role\n    async function getAllMappings(roleId) {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"role_mapping\").select().eq(\"role_id\", roleId);\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                setAllMappings(data);\n                console.log(\"All mappings\");\n                console.log(data);\n            } else {\n                console.log(\"no existing data\");\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    // update existing or create new mapping\n    const createMappingRecord = ()=>{\n        if (existingMap) {\n            updateMapping(existingMap && existingMap.id);\n        } else {\n            createMapping();\n        }\n    };\n    // create mapping for currently selected skill\n    async function createMapping() {\n        try {\n            // setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"role_mapping\").insert({\n                role_id: roleSelected,\n                skill_type: typeSelected,\n                skill_id: skillSelectedId && skillSelectedId.id,\n                skill_level: selectedLevel,\n                skill_name: skillSelectedId && skillSelectedId.skill_name\n            });\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (status == 201) {\n                console.log(status);\n                showSaveConfirm();\n            }\n        } catch (error) {\n            console.log(\"Error:\", error.message);\n            showError(error.message);\n        } finally{\n        // setLoading(false);\n        }\n    }\n    async function updateMapping(id) {\n        try {\n            // setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"role_mapping\").update({\n                skill_level: selectedLevel\n            }).eq(\"id\", id);\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (status == 204) {\n                console.log(status);\n                showSaveConfirm();\n            }\n        } catch (error) {\n            console.log(\"Error:\", error.message);\n            showError(error.message);\n        } finally{\n        // setLoading(false);\n        }\n    }\n    // delete a mapping record\n    const deleteMappingRecord = (id)=>{\n        deleteMapping(id);\n    };\n    async function deleteMapping(id) {\n        try {\n            // setLoading(true);\n            let { error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"role_mapping\").delete().eq(\"id\", id);\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (status == 204) {\n                console.log(status);\n            // showSaveConfirm();\n            }\n        } catch (error) {\n            console.log(\"Error:\", error.message);\n            showError(error.message);\n        } finally{\n        // setLoading(false);\n        }\n    }\n    // Show loading state while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid place-items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-10 mb-10 flex flex-row space-x-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(svg_loaders_react__WEBPACK_IMPORTED_MODULE_5__.Oval, {\n                        stroke: \"#0c39ac\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                        lineNumber: 580,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                    lineNumber: 579,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                lineNumber: 578,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n            lineNumber: 577,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render dashboard if no session (will redirect)\n    if (!session) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_app_sidebar__WEBPACK_IMPORTED_MODULE_8__.AppSidebar, {\n                userData: userData,\n                behavioural_skills: behavioural_skills,\n                technical_skills: technical_skills,\n                allMappings: allMappings,\n                deleteMappingRecord: deleteMappingRecord,\n                typeSelected: typeSelected\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                lineNumber: 594,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarInset, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"flex h-16 shrink-0 items-center gap-2 border-b\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 px-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarTrigger, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                    lineNumber: 606,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__.Breadcrumb, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__.BreadcrumbList, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__.BreadcrumbItem, {\n                                                className: \"hidden md:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__.BreadcrumbLink, {\n                                                    href: \"#\",\n                                                    children: \"Admin tools\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                    lineNumber: 611,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                lineNumber: 610,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__.BreadcrumbSeparator, {\n                                                className: \"hidden md:block\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                lineNumber: 613,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__.BreadcrumbItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__.BreadcrumbLink, {\n                                                    children: \"Skills mapping\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                    lineNumber: 615,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                lineNumber: 614,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                        lineNumber: 609,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                    lineNumber: 608,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                            lineNumber: 605,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                        lineNumber: 604,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-1 flex-row gap-2 p-2 pt-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex m-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-6 pt-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                onValueChange: (roleSelected)=>selectRole(roleSelected),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                        className: \"text-sm text-center pb-1 text-primary font-semibold\",\n                                                        htmlFor: \"skill type\",\n                                                        children: \"Role\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 635,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                        className: \"w-[300px]\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                            placeholder: \"Select a role\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 642,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 641,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                                        children: userRoles && userRoles.map((role)=>{\n                                                            if (role.role_name !== \"Team Owner\") {\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                    value: role.id,\n                                                                    children: role.role_name\n                                                                }, role.id, false, {\n                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                    lineNumber: 649,\n                                                                    columnNumber: 29\n                                                                }, this);\n                                                            }\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 644,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                lineNumber: 629,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                            lineNumber: 628,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-6 pt-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                // onValueChange={(skillSelected) =>\n                                                //   setTypeSelected(skillSelected)\n                                                // }\n                                                // pass the type to the get mappings query\n                                                onValueChange: (value)=>getMappings(value),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                        className: \"text-sm text-center pb-1  text-primary font-semibold\",\n                                                        htmlFor: \"skill type\",\n                                                        children: \"Skill type\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 667,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                        className: \"w-[150px]\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                            placeholder: \"Select type\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 674,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 673,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                value: \"Behavioural\",\n                                                                children: \"Behavioural\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                lineNumber: 677,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                value: \"Technical\",\n                                                                children: \"Technical\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                lineNumber: 678,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 676,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                lineNumber: 660,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                            lineNumber: 659,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-6 pt-0\",\n                                            children: [\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex ml-3 pt-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_button__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__.ChevronLeft, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 686,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 685,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                    lineNumber: 684,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                            lineNumber: 682,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3 pt-0\",\n                                            children: [\n                                                !typeSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                    disabled: true,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                            className: \"text-sm text-center pb-1 text-primary font-semibold\",\n                                                            htmlFor: \"skill type\",\n                                                            children: \"Skill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 693,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                            className: \"w-[220px]\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                                placeholder: \"Select a skill\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                lineNumber: 700,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 699,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                    lineNumber: 692,\n                                                    columnNumber: 19\n                                                }, this),\n                                                typeSelected === \"Behavioural\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_popover__WEBPACK_IMPORTED_MODULE_14__.Popover, {\n                                                    open: open,\n                                                    onOpenChange: setOpen,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                            className: \"text-sm text-center pb-1 text-primary font-semibold\",\n                                                            htmlFor: \"skill type\",\n                                                            children: \"Skill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 707,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_popover__WEBPACK_IMPORTED_MODULE_14__.PopoverTrigger, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_button__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                                                variant: \"outline\",\n                                                                role: \"combobox\",\n                                                                \"aria-expanded\": open,\n                                                                className: \"w-[300px] justify-between text-muted-foreground\",\n                                                                children: [\n                                                                    skillSelected ? (_behavioural_skills_find = behavioural_skills.find((skill)=>skill.skill_name === skillSelected)) === null || _behavioural_skills_find === void 0 ? void 0 : _behavioural_skills_find.skill_name : \"Select skill\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__.ChevronsUpDown, {\n                                                                        className: \"opacity-50\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                        lineNumber: 725,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                lineNumber: 714,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 713,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_popover__WEBPACK_IMPORTED_MODULE_14__.PopoverContent, {\n                                                            className: \"w-[200px] p-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.Command, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandInput, {\n                                                                        placeholder: \"Search skills...\",\n                                                                        className: \"h-9\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                        lineNumber: 730,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandList, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandEmpty, {\n                                                                                children: \"No match found\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                lineNumber: 735,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandGroup, {\n                                                                                children: behavioural_skills.map((skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandItem, {\n                                                                                        value: skill.skill_name,\n                                                                                        onSelect: (currentValue)=>{\n                                                                                            setSkillSelected(currentValue === skillSelected ? \"\" : currentValue);\n                                                                                            setSkillSelectedId(getSelectedRecord(skill.id));\n                                                                                            setOpen(false);\n                                                                                        },\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__.CircleCheck, {\n                                                                                                className: allMappings.find((mapping)=>mapping.skill_id === skill.id) ? \"text-yellow-500\" : \"opacity-0\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                                lineNumber: 753,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            skill.skill_name,\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__.Check, {\n                                                                                                className: (0,_core_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"ml-auto\", skillSelected === skill.skill_name ? \"opacity-100\" : \"opacity-0\")\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                                lineNumber: 763,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, skill.skill_name, true, {\n                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                        lineNumber: 738,\n                                                                                        columnNumber: 31\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                lineNumber: 736,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                        lineNumber: 734,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                lineNumber: 729,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 728,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                    lineNumber: 706,\n                                                    columnNumber: 19\n                                                }, this),\n                                                typeSelected === \"Technical\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_popover__WEBPACK_IMPORTED_MODULE_14__.Popover, {\n                                                    open: open,\n                                                    onOpenChange: setOpen,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                            className: \"text-sm text-center pb-1 text-primary font-semibold\",\n                                                            htmlFor: \"skill type\",\n                                                            children: \"Skill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 782,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_popover__WEBPACK_IMPORTED_MODULE_14__.PopoverTrigger, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_button__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                                                variant: \"outline\",\n                                                                role: \"combobox\",\n                                                                \"aria-expanded\": open,\n                                                                className: \"w-[300px] justify-between\",\n                                                                children: [\n                                                                    skillSelected ? (_technical_skills_find = technical_skills.find((skill)=>skill.skill_name === skillSelected)) === null || _technical_skills_find === void 0 ? void 0 : _technical_skills_find.skill_name : \"Select skill\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__.ChevronsUpDown, {\n                                                                        className: \"opacity-50\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                        lineNumber: 800,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                lineNumber: 789,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 788,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_popover__WEBPACK_IMPORTED_MODULE_14__.PopoverContent, {\n                                                            className: \"w-[200px] p-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.Command, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandInput, {\n                                                                        placeholder: \"Search skills...\",\n                                                                        className: \"h-9\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                        lineNumber: 805,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandList, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandEmpty, {\n                                                                                children: \"No match found\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                lineNumber: 810,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandGroup, {\n                                                                                children: technical_skills.map((skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandItem, {\n                                                                                        value: skill.skill_name,\n                                                                                        onSelect: (currentValue)=>{\n                                                                                            setSkillSelected(currentValue === skillSelected ? \"\" : currentValue);\n                                                                                            setSkillSelectedId(getSelectedRecord(skill.id));\n                                                                                            setOpen(false);\n                                                                                        },\n                                                                                        children: [\n                                                                                            skill.skill_name,\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__.Check, {\n                                                                                                className: (0,_core_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"ml-auto\", skillSelected === skill.skill_name ? \"opacity-100\" : \"opacity-0\")\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                                lineNumber: 831,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, skill.skill_name, true, {\n                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                        lineNumber: 813,\n                                                                                        columnNumber: 31\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                lineNumber: 811,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                        lineNumber: 809,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                lineNumber: 804,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 803,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                    lineNumber: 781,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                            lineNumber: 690,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-0 pt-0\",\n                                            children: [\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex ml-3=0 pt-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_button__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                                        className: \"ml-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__.ChevronRight, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 852,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 851,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                    lineNumber: 850,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                            lineNumber: 848,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex ml-3 pt-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_button__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                                className: \"bg-green-600 hover:bg-green-600/90\",\n                                                onClick: createMappingRecord,\n                                                children: \"Save selection\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                lineNumber: 858,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                            lineNumber: 857,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                    lineNumber: 627,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                lineNumber: 626,\n                                columnNumber: 11\n                            }, this),\n                            skillSelected && typeSelected === \"Behavioural\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(core_BSkillsLib__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(core_MapperB_MapperBSkillsLibHeader__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                        lineNumber: 870,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(core_MapperB_MapperBSkillRow__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        behavioural_sub_skills: skillSelectedId.behavioural_sub_skills,\n                                        handleOnChange: handleOnChange,\n                                        lvl1: lvl1,\n                                        lvl2: lvl2,\n                                        lvl3: lvl3,\n                                        lvl4: lvl4,\n                                        lvl5: lvl5\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                        lineNumber: 871,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                lineNumber: 869,\n                                columnNumber: 13\n                            }, this),\n                            skillSelected && typeSelected === \"Technical\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(core_TSkillsLib__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(core_Mapper_MapperSkillsLibHeader__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                        lineNumber: 885,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(core_Mapper_MapperSkillRow__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        technical_sub_skills: skillSelectedId.technical_sub_skills,\n                                        handleOnChange: handleOnChange,\n                                        lvl1: lvl1,\n                                        lvl2: lvl2,\n                                        lvl3: lvl3,\n                                        lvl4: lvl4\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                        lineNumber: 886,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                lineNumber: 884,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_Toaster__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                position: \"bottom-right\",\n                                toastOptions: {\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                lineNumber: 896,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                        lineNumber: 621,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                lineNumber: 603,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n        lineNumber: 593,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"gldjzi+fY/08eNFn7LFFxJAkRSI=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/admin/skills-mapping.js\n"));

/***/ })

});