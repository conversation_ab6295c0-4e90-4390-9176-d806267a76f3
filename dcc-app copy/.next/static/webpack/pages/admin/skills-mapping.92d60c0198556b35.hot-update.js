"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/admin/skills-mapping",{

/***/ "(pages-dir-browser)/./pages/admin/skills-mapping.js":
/*!***************************************!*\
  !*** ./pages/admin/skills-mapping.js ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSG: () => (/* binding */ __N_SSG),\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(pages-dir-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @utils/supabaseClient */ \"(pages-dir-browser)/./utils/supabaseClient.js\");\n/* harmony import */ var svg_loaders_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! svg-loaders-react */ \"(pages-dir-browser)/./node_modules/svg-loaders-react/dist/index.js\");\n/* harmony import */ var svg_loaders_react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(svg_loaders_react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @core/components/ui/sidebar */ \"(pages-dir-browser)/./core/components/ui/sidebar.jsx\");\n/* harmony import */ var _core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @core/components/ui/breadcrumb */ \"(pages-dir-browser)/./core/components/ui/breadcrumb.jsx\");\n/* harmony import */ var _core_components_app_sidebar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @core/components/app-sidebar */ \"(pages-dir-browser)/./core/components/app-sidebar.jsx\");\n/* harmony import */ var _core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @core/components/ui/select */ \"(pages-dir-browser)/./core/components/ui/select.jsx\");\n/* harmony import */ var _core_components_ui_label__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @core/components/ui/label */ \"(pages-dir-browser)/./core/components/ui/label.jsx\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,ChevronsUpDown,CircleCheck!=!lucide-react */ \"(pages-dir-browser)/__barrel_optimize__?names=Check,ChevronLeft,ChevronRight,ChevronsUpDown,CircleCheck!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _core_lib_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @core/lib/utils */ \"(pages-dir-browser)/./core/lib/utils.js\");\n/* harmony import */ var _core_components_ui_button__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @core/components/ui/button */ \"(pages-dir-browser)/./core/components/ui/button.jsx\");\n/* harmony import */ var _core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @core/components/ui/command */ \"(pages-dir-browser)/./core/components/ui/command.jsx\");\n/* harmony import */ var _core_components_ui_popover__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @core/components/ui/popover */ \"(pages-dir-browser)/./core/components/ui/popover.jsx\");\n/* harmony import */ var core_TSkillsLib__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! core/TSkillsLib */ \"(pages-dir-browser)/./core/TSkillsLib.js\");\n/* harmony import */ var core_BSkillsLib__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! core/BSkillsLib */ \"(pages-dir-browser)/./core/BSkillsLib.js\");\n/* harmony import */ var core_Mapper_MapperSkillsLibHeader__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! core/Mapper/MapperSkillsLibHeader */ \"(pages-dir-browser)/./core/Mapper/MapperSkillsLibHeader.js\");\n/* harmony import */ var core_Mapper_MapperSkillRow__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! core/Mapper/MapperSkillRow */ \"(pages-dir-browser)/./core/Mapper/MapperSkillRow.js\");\n/* harmony import */ var core_MapperB_MapperBSkillsLibHeader__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! core/MapperB/MapperBSkillsLibHeader */ \"(pages-dir-browser)/./core/MapperB/MapperBSkillsLibHeader.js\");\n/* harmony import */ var core_MapperB_MapperBSkillRow__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! core/MapperB/MapperBSkillRow */ \"(pages-dir-browser)/./core/MapperB/MapperBSkillRow.js\");\n/* harmony import */ var _core_Toaster__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @core/Toaster */ \"(pages-dir-browser)/./core/Toaster.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar __N_SSG = true;\nfunction Dashboard(param) {\n    let { behavioural_skills, technical_skills } = param;\n    var _behavioural_skills_find, _technical_skills_find;\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userRole, setUserRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userRoles, setUserRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [typeSelected, setTypeSelected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [roleSelected, setRoleSelected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [skillSelected, setSkillSelected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [skillSelectedId, setSkillSelectedId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [value, setValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedLevel, setSelectedLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [existingMap, setExistingMap] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [allMappings, setAllMappings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            // Check for existing session\n            _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getSession().then({\n                \"Dashboard.useEffect\": (param)=>{\n                    let { data: { session } } = param;\n                    setSession(session);\n                    if (!session) {\n                        // Redirect to index page if no session\n                        router.push(\"/\");\n                    }\n                }\n            }[\"Dashboard.useEffect\"]);\n            // Listen for auth state changes\n            const { data: { subscription } } = _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.onAuthStateChange({\n                \"Dashboard.useEffect\": (_event, session)=>{\n                    setSession(session);\n                    if (!session) {\n                        // Redirect to index page if session is lost\n                        router.push(\"/\");\n                    }\n                }\n            }[\"Dashboard.useEffect\"]);\n            return ({\n                \"Dashboard.useEffect\": ()=>subscription.unsubscribe()\n            })[\"Dashboard.useEffect\"];\n        }\n    }[\"Dashboard.useEffect\"], [\n        router\n    ]);\n    // Fetch user profile when session is available\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if (session) {\n                getUserProfile();\n            }\n        }\n    }[\"Dashboard.useEffect\"], [\n        session\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if (skillSelectedId) {\n                getMappings();\n            }\n        }\n    }[\"Dashboard.useEffect\"], [\n        skillSelected\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if (roleSelected) {\n                getAllMappings(roleSelected);\n            }\n        }\n    }[\"Dashboard.useEffect\"], [\n        roleSelected,\n        skillSelected\n    ]);\n    /* --- DEBUG --- */ // console.log(\"existingMap\");\n    // console.log(existingMap);\n    console.log(\"typeSelected\");\n    console.log(typeSelected);\n    // console.log(\"allMappings\");\n    // console.log(allMappings);\n    console.log(\"roleSelected\");\n    console.log(roleSelected);\n    // console.log(\"skillSelected\");\n    // console.log(skillSelected);\n    // console.log(\"skillSelectedId\");\n    // console.log(skillSelectedId && skillSelectedId.id);\n    // console.log(\"selectedLevel\");\n    // console.log(selectedLevel);\n    // console.log(\"userData\");\n    // console.log(userData);\n    // console.log(\"userRole\");\n    // console.log(userRole);\n    // console.log(\"userRoles\");\n    // console.log(userRoles);\n    // console.log(\"behavioural_skills\");\n    // console.log(behavioural_skills);\n    // console.log(\"technical_skills\");\n    // console.log(technical_skills);\n    /* --- DEBUG --- */ const [lvl1, setLvl1] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lvl2, setLvl2] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lvl3, setLvl3] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lvl4, setLvl4] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lvl5, setLvl5] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // console.log(isChecked ? \"checked\" : \"unchecked\");\n    const handleOnChange = (level)=>{\n        // Reset all levels if the current level is being unchecked\n        if (level === 1 && lvl1 || level === 2 && lvl2 || level === 3 && lvl3 || level === 4 && lvl4 || level === 5 && lvl5) {\n            setLvl1(false);\n            setLvl2(false);\n            setLvl3(false);\n            setLvl4(false);\n            setLvl5(false);\n            return;\n        }\n        // Set all levels to false first\n        setLvl1(false);\n        setLvl2(false);\n        setLvl3(false);\n        setLvl4(false);\n        setLvl5(false);\n        // Then set only the selected level to true\n        switch(level){\n            case 1:\n                setLvl1(true);\n                setSelectedLevel(1);\n                break;\n            case 2:\n                setLvl2(true);\n                setSelectedLevel(2);\n                break;\n            case 3:\n                setLvl3(true);\n                setSelectedLevel(3);\n                break;\n            case 4:\n                setLvl4(true);\n                setSelectedLevel(4);\n                break;\n            case 5:\n                setLvl5(true);\n                setSelectedLevel(5);\n                break;\n        }\n    };\n    // select role\n    const selectRole = (roleSelected)=>{\n        console.log(\"roleSelected\");\n        setRoleSelected(roleSelected);\n    // setTypeSelected(null);\n    // setSkillSelectedId(null);\n    };\n    const showSaveConfirm = ()=>{\n        sonner__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Mapping created\", {\n            classNames: {\n                icon: \"!text-green-600\"\n            }\n        });\n    };\n    const showError = ()=>{\n        sonner__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Error - not saved, make sure level is selected\", {\n            classNames: {\n                icon: \"!text-red-600\"\n            }\n        });\n    };\n    const getSelectedRecord = (skill_id)=>{\n        // if behavioural or technical, get the record from the array\n        if (typeSelected === \"Behavioural\") {\n            const selectedSkillsRec = behavioural_skills.find((record)=>record.id === skill_id);\n            return selectedSkillsRec;\n        }\n        if (typeSelected === \"Technical\") {\n            const selectedSkillsRec = technical_skills.find((record)=>record.id === skill_id);\n            return selectedSkillsRec;\n        }\n    };\n    async function getUserProfile() {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"profiles\").select(\"*\").eq(\"id\", user.id).single();\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                setUserData(data);\n                getUserRole(data.team_role);\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    // get user's role\n    async function getUserRole(id) {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"roles\").select(\"id, team\").eq(\"id\", id).single();\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                setUserRole(data);\n                getRoles(data.team);\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    // get roles based on user\n    async function getRoles(team) {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"roles\").select(\"id, team, role_name\").eq(\"team\", team);\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                setUserRoles(data);\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    // get existing mappings based on user's team\n    async function getMappings() {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"role_mapping\").select().eq(\"skill_id\", skillSelectedId && skillSelectedId.id) // add role here too!\n            .eq(\"role_id\", roleSelected).single();\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                setExistingMap(data);\n                handleOnChange(data.skill_level);\n            } else {\n                console.log(\"no existing data\");\n                handleOnChange(null);\n                setSelectedLevel(null);\n                setExistingMap(null);\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    // get all existing mappings for the role\n    async function getAllMappings(roleId) {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"role_mapping\").select().eq(\"role_id\", roleId);\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                setAllMappings(data);\n            } else {\n                console.log(\"no existing data\");\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    // update existing or create new mapping\n    const createMappingRecord = ()=>{\n        if (existingMap) {\n            updateMapping(existingMap && existingMap.id);\n        } else {\n            createMapping();\n        }\n    };\n    // create mapping for currently selected skill\n    async function createMapping() {\n        try {\n            // setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"role_mapping\").insert({\n                role_id: roleSelected,\n                skill_type: typeSelected,\n                skill_id: skillSelectedId && skillSelectedId.id,\n                skill_level: selectedLevel,\n                skill_name: skillSelectedId && skillSelectedId.skill_name\n            });\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (status == 201) {\n                console.log(status);\n                showSaveConfirm();\n            }\n        } catch (error) {\n            console.log(\"Error:\", error.message);\n            showError(error.message);\n        } finally{\n        // setLoading(false);\n        }\n    }\n    async function updateMapping(id) {\n        try {\n            // setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"role_mapping\").update({\n                skill_level: selectedLevel\n            }).eq(\"id\", id);\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (status == 204) {\n                console.log(status);\n                showSaveConfirm();\n            }\n        } catch (error) {\n            console.log(\"Error:\", error.message);\n            showError(error.message);\n        } finally{\n        // setLoading(false);\n        }\n    }\n    // delete a mapping record\n    const deleteMappingRecord = (id)=>{\n        deleteMapping(id);\n    };\n    async function deleteMapping(id) {\n        try {\n            // setLoading(true);\n            let { error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"role_mapping\").delete().eq(\"id\", id);\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (status == 204) {\n                console.log(status);\n            // showSaveConfirm();\n            }\n        } catch (error) {\n            console.log(\"Error:\", error.message);\n            showError(error.message);\n        } finally{\n        // setLoading(false);\n        }\n    }\n    // Show loading state while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid place-items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-10 mb-10 flex flex-row space-x-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(svg_loaders_react__WEBPACK_IMPORTED_MODULE_5__.Oval, {\n                        stroke: \"#0c39ac\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                        lineNumber: 568,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                    lineNumber: 567,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                lineNumber: 566,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n            lineNumber: 565,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render dashboard if no session (will redirect)\n    if (!session) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_app_sidebar__WEBPACK_IMPORTED_MODULE_8__.AppSidebar, {\n                userData: userData,\n                behavioural_skills: behavioural_skills,\n                technical_skills: technical_skills,\n                allMappings: allMappings,\n                deleteMappingRecord: deleteMappingRecord,\n                typeSelected: typeSelected\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                lineNumber: 582,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarInset, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"flex h-16 shrink-0 items-center gap-2 border-b\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 px-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarTrigger, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                    lineNumber: 594,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__.Breadcrumb, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__.BreadcrumbList, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__.BreadcrumbItem, {\n                                                className: \"hidden md:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__.BreadcrumbLink, {\n                                                    href: \"#\",\n                                                    children: \"Admin tools\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                    lineNumber: 599,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                lineNumber: 598,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__.BreadcrumbSeparator, {\n                                                className: \"hidden md:block\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                lineNumber: 601,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__.BreadcrumbItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_7__.BreadcrumbLink, {\n                                                    children: \"Skills mapping\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                    lineNumber: 603,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                lineNumber: 602,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                        lineNumber: 597,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                    lineNumber: 596,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                            lineNumber: 593,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                        lineNumber: 592,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-1 flex-row gap-2 p-2 pt-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex m-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-6 pt-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                onValueChange: (roleSelected)=>selectRole(roleSelected),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                        className: \"text-sm text-center pb-1 text-primary font-semibold\",\n                                                        htmlFor: \"role\",\n                                                        children: \"Role\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 623,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                        className: \"w-[300px]\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                            placeholder: \"Select a role\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 630,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 629,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                                        children: userRoles && userRoles.map((role)=>{\n                                                            if (role.role_name !== \"Team Owner\") {\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                    value: role.id,\n                                                                    children: role.role_name\n                                                                }, role.id, false, {\n                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                    lineNumber: 637,\n                                                                    columnNumber: 29\n                                                                }, this);\n                                                            }\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 632,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                lineNumber: 617,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                            lineNumber: 616,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-6 pt-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                disabled: !roleSelected,\n                                                // onValueChange={(roleSelected) =>\n                                                //   setRoleSelected(roleSelected)\n                                                // }\n                                                onValueChange: ()=>router.push(\"/admin\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                        className: \"text-sm text-center pb-1  text-primary font-semibold\",\n                                                        htmlFor: \"skill type\",\n                                                        children: \"Skill type\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 655,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                        className: \"w-[150px]\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                            placeholder: \"Select type\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 662,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 661,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                value: \"skills-mapping-behavioural\",\n                                                                children: \"Behavioural\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                lineNumber: 665,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                value: \"skills-mapping-technical\",\n                                                                children: \"Technical\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                lineNumber: 668,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 664,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                lineNumber: 648,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                            lineNumber: 647,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-6 pt-0\",\n                                            children: [\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex ml-3 pt-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_button__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                                        disabled: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__.ChevronLeft, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 678,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 677,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                    lineNumber: 676,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                            lineNumber: 674,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3 pt-0\",\n                                            children: [\n                                                !typeSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                    disabled: true,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                            className: \"text-sm text-center pb-1 text-primary font-semibold\",\n                                                            htmlFor: \"skill type\",\n                                                            children: \"Skill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 685,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                            className: \"w-[220px]\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                                placeholder: \"Select a skill\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                lineNumber: 692,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 691,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                    lineNumber: 684,\n                                                    columnNumber: 19\n                                                }, this),\n                                                typeSelected === \"Behavioural\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_popover__WEBPACK_IMPORTED_MODULE_14__.Popover, {\n                                                    open: open,\n                                                    onOpenChange: setOpen,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                            className: \"text-sm text-center pb-1 text-primary font-semibold\",\n                                                            htmlFor: \"skill type\",\n                                                            children: \"Skill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 699,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_popover__WEBPACK_IMPORTED_MODULE_14__.PopoverTrigger, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_button__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                                                variant: \"outline\",\n                                                                role: \"combobox\",\n                                                                \"aria-expanded\": open,\n                                                                className: \"w-[300px] justify-between text-muted-foreground\",\n                                                                children: [\n                                                                    skillSelected ? (_behavioural_skills_find = behavioural_skills.find((skill)=>skill.skill_name === skillSelected)) === null || _behavioural_skills_find === void 0 ? void 0 : _behavioural_skills_find.skill_name : \"Select skill\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__.ChevronsUpDown, {\n                                                                        className: \"opacity-50\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                        lineNumber: 717,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                lineNumber: 706,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 705,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_popover__WEBPACK_IMPORTED_MODULE_14__.PopoverContent, {\n                                                            className: \"w-[200px] p-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.Command, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandInput, {\n                                                                        placeholder: \"Search skills...\",\n                                                                        className: \"h-9\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                        lineNumber: 722,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandList, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandEmpty, {\n                                                                                children: \"No match found\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                lineNumber: 727,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandGroup, {\n                                                                                children: behavioural_skills.map((skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandItem, {\n                                                                                        value: skill.skill_name,\n                                                                                        onSelect: (currentValue)=>{\n                                                                                            setSkillSelected(currentValue === skillSelected ? \"\" : currentValue);\n                                                                                            setSkillSelectedId(getSelectedRecord(skill.id));\n                                                                                            setOpen(false);\n                                                                                        },\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__.CircleCheck, {\n                                                                                                className: allMappings.find((mapping)=>mapping.skill_id === skill.id) ? \"text-yellow-500\" : \"opacity-0\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                                lineNumber: 745,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            skill.skill_name,\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__.Check, {\n                                                                                                className: (0,_core_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"ml-auto\", skillSelected === skill.skill_name ? \"opacity-100\" : \"opacity-0\")\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                                lineNumber: 755,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, skill.skill_name, true, {\n                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                        lineNumber: 730,\n                                                                                        columnNumber: 31\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                lineNumber: 728,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                        lineNumber: 726,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                lineNumber: 721,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 720,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                    lineNumber: 698,\n                                                    columnNumber: 19\n                                                }, this),\n                                                typeSelected === \"Technical\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_popover__WEBPACK_IMPORTED_MODULE_14__.Popover, {\n                                                    open: open,\n                                                    onOpenChange: setOpen,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                            className: \"text-sm text-center pb-1 text-primary font-semibold\",\n                                                            htmlFor: \"skill type\",\n                                                            children: \"Skill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 774,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_popover__WEBPACK_IMPORTED_MODULE_14__.PopoverTrigger, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_button__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                                                variant: \"outline\",\n                                                                role: \"combobox\",\n                                                                \"aria-expanded\": open,\n                                                                className: \"w-[300px] justify-between\",\n                                                                children: [\n                                                                    skillSelected ? (_technical_skills_find = technical_skills.find((skill)=>skill.skill_name === skillSelected)) === null || _technical_skills_find === void 0 ? void 0 : _technical_skills_find.skill_name : \"Select skill\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__.ChevronsUpDown, {\n                                                                        className: \"opacity-50\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                        lineNumber: 792,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                lineNumber: 781,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 780,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_popover__WEBPACK_IMPORTED_MODULE_14__.PopoverContent, {\n                                                            className: \"w-[200px] p-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.Command, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandInput, {\n                                                                        placeholder: \"Search skills...\",\n                                                                        className: \"h-9\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                        lineNumber: 797,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandList, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandEmpty, {\n                                                                                children: \"No match found\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                lineNumber: 802,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandGroup, {\n                                                                                children: technical_skills.map((skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_13__.CommandItem, {\n                                                                                        value: skill.skill_name,\n                                                                                        onSelect: (currentValue)=>{\n                                                                                            setSkillSelected(currentValue === skillSelected ? \"\" : currentValue);\n                                                                                            setSkillSelectedId(getSelectedRecord(skill.id));\n                                                                                            setOpen(false);\n                                                                                        },\n                                                                                        children: [\n                                                                                            skill.skill_name,\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__.Check, {\n                                                                                                className: (0,_core_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"ml-auto\", skillSelected === skill.skill_name ? \"opacity-100\" : \"opacity-0\")\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                                lineNumber: 823,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, skill.skill_name, true, {\n                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                        lineNumber: 805,\n                                                                                        columnNumber: 31\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                                lineNumber: 803,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                        lineNumber: 801,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                                lineNumber: 796,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 795,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                    lineNumber: 773,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                            lineNumber: 682,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-0 pt-0\",\n                                            children: [\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex ml-3=0 pt-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_button__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                                        disabled: true,\n                                                        className: \"ml-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_22__.ChevronRight, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                            lineNumber: 844,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                        lineNumber: 843,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                    lineNumber: 842,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                            lineNumber: 840,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex ml-3 pt-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_button__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                                disabled: true,\n                                                className: \"bg-green-600 hover:bg-green-600/90\",\n                                                onClick: createMappingRecord,\n                                                children: \"Save selection\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                                lineNumber: 850,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                            lineNumber: 849,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                    lineNumber: 615,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                lineNumber: 614,\n                                columnNumber: 11\n                            }, this),\n                            skillSelected && typeSelected === \"Behavioural\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(core_BSkillsLib__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(core_MapperB_MapperBSkillsLibHeader__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                        lineNumber: 863,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(core_MapperB_MapperBSkillRow__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        behavioural_sub_skills: skillSelectedId.behavioural_sub_skills,\n                                        handleOnChange: handleOnChange,\n                                        lvl1: lvl1,\n                                        lvl2: lvl2,\n                                        lvl3: lvl3,\n                                        lvl4: lvl4,\n                                        lvl5: lvl5\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                        lineNumber: 864,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                lineNumber: 862,\n                                columnNumber: 13\n                            }, this),\n                            skillSelected && typeSelected === \"Technical\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(core_TSkillsLib__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(core_Mapper_MapperSkillsLibHeader__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                        lineNumber: 878,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(core_Mapper_MapperSkillRow__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        technical_sub_skills: skillSelectedId.technical_sub_skills,\n                                        handleOnChange: handleOnChange,\n                                        lvl1: lvl1,\n                                        lvl2: lvl2,\n                                        lvl3: lvl3,\n                                        lvl4: lvl4\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                        lineNumber: 879,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                lineNumber: 877,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_Toaster__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                position: \"bottom-right\",\n                                toastOptions: {\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                                lineNumber: 889,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                        lineNumber: 609,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n                lineNumber: 591,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/admin/skills-mapping.js\",\n        lineNumber: 581,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"ewPeTSwEWAr25UpaT+3MTUspl0M=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/admin/skills-mapping.js\n"));

/***/ })

});