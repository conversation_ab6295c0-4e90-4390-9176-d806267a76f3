import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import { supabase } from "@utils/supabaseClient";
import { Oval } from "svg-loaders-react";
import {
  SidebarProvider,
  SidebarTrigger,
  SidebarInset,
} from "@core/components/ui/sidebar";

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@core/components/ui/breadcrumb";

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@core/components/ui/popover";

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@core/components/ui/card";

import { AppSidebar } from "@core/components/app-sidebar";
import BSkillsLib from "core/BSkillsLib";
import BSkillsLibHeader from "core/BSkills/BSkillsLibHeader";
import BSkillsLibRow from "core/BSkills/BSkillRow";

BSkillsLibHeader;

export default function Behavioural({
  behavioural_skills,
  behavioural_skills_menu,
  technical_skills_menu,
}) {
  const [loading, setLoading] = useState(true);
  const [session, setSession] = useState(null);
  const [userData, setUserData] = useState(null);

  const router = useRouter();

  useEffect(() => {
    // Check for existing session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      if (!session) {
        // Redirect to index page if no session
        router.push("/");
      }
    });

    // Listen for auth state changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
      if (!session) {
        // Redirect to index page if session is lost
        router.push("/");
      }
    });

    return () => subscription.unsubscribe();
  }, [router]);

  // Fetch user profile when session is available
  useEffect(() => {
    if (session) {
      getUserProfile();
    }
  }, [session]);

  /* --- DEBUG --- */
  // console.log("behavioural_skills");
  // console.log(behavioural_skills);

  // console.log("behavioural_skills_menu");
  // console.log(behavioural_skills_menu);

  /* --- DEBUG --- */

  async function getUserProfile() {
    try {
      setLoading(true);

      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        throw new Error("No user found");
      }

      let { data, error, status } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", user.id)
        .single();

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        setUserData(data);
      }
    } catch (error) {
      console.log("Error fetching user profile:", error.message);
      // If there's an error fetching profile, we can still show the dashboard
      // but userData will remain null
    } finally {
      setLoading(false);
    }
  }

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="grid place-items-center">
          <div className="mt-10 mb-10 flex flex-row space-x-2">
            <Oval stroke="#0c39ac" />
          </div>
        </div>
      </div>
    );
  }

  // Don't render dashboard if no session (will redirect)
  if (!session) {
    return null;
  }

  return (
    <SidebarProvider>
      <AppSidebar
        userData={userData}
        behavioural_skills={behavioural_skills_menu}
        technical_skills={technical_skills_menu}
        selected_item={behavioural_skills.skill_name}
      />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b">
          <div className="flex items-center gap-2 px-3">
            <SidebarTrigger />

            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="#">Skills Library</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbLink>Behavioural</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>
                    {behavioural_skills.skill_name}
                  </BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>

            <div className="flex items-center gap-2 px-4">
              <Popover>
                <PopoverTrigger>
                  {/* <span className="border border-gray-400 p-1.5 rounded-md text-sm bg-[#ca005d] text-white">
                How
              </span> */}
                  <span className="border border-gray-400 p-1.5 rounded-md text-sm">
                    How
                  </span>
                </PopoverTrigger>
                <PopoverContent>{behavioural_skills.how}</PopoverContent>
              </Popover>
              <Popover>
                <PopoverTrigger>
                  {/* <span className="border border-gray-400 p-1.5 rounded-md text-sm  bg-[#ca005d]  text-white">
                Why
              </span> */}
                  <span className="border border-gray-400 p-1.5 rounded-md text-sm">
                    Why
                  </span>
                </PopoverTrigger>
                <PopoverContent>{behavioural_skills.why}</PopoverContent>
              </Popover>
            </div>
          </div>
        </header>

        <main>
          <BSkillsLib>
            <BSkillsLibHeader />
            <BSkillsLibRow
              skill_name={behavioural_skills.skill_name}
              behavioural_sub_skills={behavioural_skills.behavioural_sub_skills}
            />
          </BSkillsLib>
        </main>
      </SidebarInset>
    </SidebarProvider>
  );
}

export async function getStaticPaths() {
  const { data, error } = await supabase.from("behavioural_skills").select("*");

  const behavioural_skills = data;
  // const paths = [];

  const paths =
    data &&
    data.map((skill) => ({
      params: { id: skill.id.toString() },
    }));

  return {
    paths,
    fallback: false,
  };
}

export async function getStaticProps({ params }) {
  const { id } = params;

  let behavioural_skills;
  let behavioural_skills_menu;
  let technical_skills_menu;

  try {
    const { data, error } = await supabase
      .from("behavioural_skills")
      .select(
        "id, group, skill_name, how, why, behavioural_sub_skills (id, sub_skill_name,level_1_description, level_2_description, level_3_description, level_4_description, level_5_description, index)"
      )
      .eq("id", id)
      .single();

    if (data) {
      console.log(data);
      behavioural_skills = data;

      // map through the array and extract just the ids to a new array
    }
  } catch (error) {
    console.log(error);
  } finally {
    try {
      const { data, error } = await supabase
        .from("behavioural_skills")
        .select(
          "id, group, skill_name, behavioural_sub_skills (id, sub_skill_name, index)"
        );

      if (data) {
        console.log(data);
        behavioural_skills_menu = data;
      }
    } catch (error) {
      console.log(error);
    } finally {
      try {
        const { data, error } = await supabase
          .from("technical_skills")
          .select("id, skill_name, technical_sub_skills (id, sub_skill_name)");
        if (data) {
          console.log(data);
          technical_skills_menu = data;
        }
      } catch (error) {
        console.log(error);
      } finally {
        console.log("done");
      }
    }
  }
  return {
    props: {
      behavioural_skills,
      behavioural_skills_menu,
      technical_skills_menu,
    },
  };
}
