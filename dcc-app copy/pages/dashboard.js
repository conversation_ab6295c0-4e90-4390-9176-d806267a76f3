import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import { supabase } from "@utils/supabaseClient";
import { Oval } from "svg-loaders-react";
import {
  SidebarProvider,
  SidebarTrigger,
  SidebarInset,
} from "@core/components/ui/sidebar";

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@core/components/ui/breadcrumb";
import { AppSidebar } from "@core/components/app-sidebar";


export default function Dashboard({ behavioural_skills, technical_skills }) {
  const [loading, setLoading] = useState(true);
  const [session, setSession] = useState(null);
  const [userData, setUserData] = useState(null);

  const router = useRouter();

  useEffect(() => {
    // Check for existing session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      if (!session) {
        // Redirect to index page if no session
        router.push("/");
      }
    });

    // Listen for auth state changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
      if (!session) {
        // Redirect to index page if session is lost
        router.push("/");
      }
    });

    return () => subscription.unsubscribe();
  }, [router]);

  // Fetch user profile when session is available
  useEffect(() => {
    if (session) {
      getUserProfile();
    }
  }, [session]);

  /* --- DEBUG --- */
  // console.log("behavioural_skills");
  // console.log(behavioural_skills);

  // console.log("technical_skills");
  // console.log(technical_skills);

  /* --- DEBUG --- */

  async function getUserProfile() {
    try {
      setLoading(true);

      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        throw new Error("No user found");
      }

      let { data, error, status } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", user.id)
        .single();

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        setUserData(data);
      }
    } catch (error) {
      console.log("Error fetching user profile:", error.message);
      // If there's an error fetching profile, we can still show the dashboard
      // but userData will remain null
    } finally {
      setLoading(false);
    }
  }

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="grid place-items-center">
          <div className="mt-10 mb-10 flex flex-row space-x-2">
            <Oval stroke="#0c39ac" />
          </div>
        </div>
      </div>
    );
  }

  // Don't render dashboard if no session (will redirect)
  if (!session) {
    return null;
  }

  return (
    <SidebarProvider>
      <AppSidebar
        userData={userData}
        behavioural_skills={behavioural_skills}
        technical_skills={technical_skills}
      />

      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b">
          <div className="flex items-center gap-2 px-3">
            <SidebarTrigger />

            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="#">Dashboard</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbLink>My Skills Snapshot</BreadcrumbLink>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>
      </SidebarInset>
    </SidebarProvider>
  );
}

// export async function getStaticProps() {
//   const { data, error } = await supabase
//     .from("behavioural_skills")
//     .select(
//       "id, group, skill_name, behavioural_sub_skills (id, sub_skill_name)"
//     );

//   if (data) {
//     console.log(data);
//   }

//   if (error) {
//     console.log(error);
//   }

//   const behavioural_skills = data;

//   return {
//     props: { behavioural_skills },
//   };
// }

export const getStaticProps = async () => {
  const bdata = await supabase
    .from("behavioural_skills")
    .select(
      "id, group, skill_name, behavioural_sub_skills (id, sub_skill_name)"
    );

  const techdata = await supabase
    .from("technical_skills")
    .select("id, skill_name, technical_sub_skills (id, sub_skill_name)");

  const responses = await Promise.all([bdata, techdata]);

  return {
    props: {
      behavioural_skills: responses[0].data,
      technical_skills: responses[1].data,
    },
  };
};
