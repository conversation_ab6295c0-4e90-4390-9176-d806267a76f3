import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import { supabase } from "@utils/supabaseClient";
import LoginForm from "core/LoginForm";
import { Oval } from "svg-loaders-react";

export default function Home() {
  const [loading, setLoading] = useState(true);
  const [session, setSession] = useState(null);
  const router = useRouter();

  useEffect(() => {
    // Check for existing session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      if (session) {
        // Redirect to dashboard if user is logged in
        router.push("/dashboard");
      } else {
        setLoading(false);
      }
    });

    // Listen for auth state changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
      if (session) {
        // Redirect to dashboard if user logs in
        router.push("/dashboard");
      }
    });

    return () => subscription.unsubscribe();
  }, [router]);

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="grid place-items-center">
          <div className="mt-10 mb-10 flex flex-row space-x-2">
            <Oval stroke="#0c39ac" />
          </div>
        </div>
      </div>
    );
  }

  // Don't render login form if user is logged in (will redirect)
  if (session) {
    return null;
  }

  return (
    <main>
      <div className="flex h-screen">
        <div className="m-auto">
          <LoginForm />
        </div>
      </div>
    </main>
  );
}
